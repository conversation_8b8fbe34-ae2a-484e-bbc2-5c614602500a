/**
 * 应用自动初始化模块
 * 在服务器启动时自动执行初始化逻辑
 */

import { initializeApp } from './startup';

// 标记是否已经初始化
let isInitialized = false;
let initPromise: Promise<void> | null = null;

/**
 * 自动初始化函数
 * 确保只执行一次，支持异步初始化
 */
export function autoInitialize(): Promise<void> {
  if (isInitialized) {
    return Promise.resolve();
  }

  if (initPromise) {
    return initPromise;
  }

  // 只在服务器端执行
  if (typeof window === 'undefined') {
    initPromise = (async () => {
      try {
        console.log('🔄 自动初始化应用...');

        // 检查是否在Vercel环境
        const isVercel = process.env.VERCEL === '1';
        if (isVercel) {
          console.log('🌐 检测到Vercel环境，跳过定时任务初始化（使用Cron Jobs代替）');
          // 在Vercel环境下，设置环境变量禁用定时任务
          process.env.DISABLE_CRAWLER_SCHEDULER = 'true';
        }

        // 延迟一点时间，确保数据库连接等已经准备好
        await new Promise(resolve => setTimeout(resolve, 1000));

        initializeApp();
        isInitialized = true;
        console.log('✅ 自动初始化完成');
      } catch (error) {
        console.error('❌ 自动初始化失败:', error);
        // 重置状态，允许重试
        initPromise = null;
        // 不抛出错误，避免影响应用启动
      }
    })();

    return initPromise;
  }

  return Promise.resolve();
}

/**
 * 强制重新初始化
 */
export function forceReinitialize(): Promise<void> {
  isInitialized = false;
  initPromise = null;
  return autoInitialize();
}

// 立即执行自动初始化
autoInitialize();
