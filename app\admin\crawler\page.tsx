'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../components/AuthProvider';

interface LoginSession {
  id: string;
  status: 'pending' | 'scanned' | 'confirmed' | 'expired' | 'failed';
  createdAt: string;
  expiresAt: string;
  userInfo?: {
    nickname: string;
    avatar: string;
  };
}

export default function CrawlerManagement() {
  const { token } = useAuth();
  const [sessions, setSessions] = useState<LoginSession[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [cookieInfo, setCookieInfo] = useState<any>(null);
  const [showQRModal, setShowQRModal] = useState(false);
  const [currentQRCode, setCurrentQRCode] = useState<string>('');
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  const [networkError, setNetworkError] = useState<string>('');
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingValues, setEditingValues] = useState({
    wechat_token: '',
    data_ticket: '',
    rand_info: '',
    bizuin: '',
    slave_user: '',
    slave_sid: ''
  });

  useEffect(() => {
    // 初始加载
    const loadData = async () => {
      setNetworkError('');
      try {
        await Promise.all([fetchSessions(), fetchCookieInfo()]);
        setLastRefreshTime(new Date());
      } catch (error) {
        setNetworkError('网络连接失败，请检查服务器状态');
      }
    };

    loadData();
  }, [token]);

  // 添加扫码状态轮询
  useEffect(() => {
    let pollInterval: NodeJS.Timeout | null = null;

    if (showQRModal && currentSessionId) {
      setIsPolling(true);
      // 当显示二维码时，每3秒轮询一次状态
      pollInterval = setInterval(async () => {
        try {
          console.log('🔄 轮询扫码状态...');
          await fetchSessions();
        } catch (error) {
          console.error('轮询状态失败:', error);
        }
      }, 3000);
    } else {
      setIsPolling(false);
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval);
      }
      setIsPolling(false);
    };
  }, [showQRModal, currentSessionId]);

  // 监听登录状态变化，自动关闭二维码模态框
  useEffect(() => {
    if (currentSessionId && showQRModal) {
      const currentSession = sessions.find(s => s.id === currentSessionId);
      if (currentSession && (currentSession.status === 'confirmed' || currentSession.status === 'expired' || currentSession.status === 'failed')) {
        // 登录完成或失败时自动关闭模态框
        setTimeout(() => {
          setShowQRModal(false);
          setCurrentQRCode('');
          setCurrentSessionId('');

          if (currentSession.status === 'confirmed') {
            // 登录成功后刷新cookie信息
            console.log('🔄 登录成功，刷新Cookie信息...');
            setTimeout(async () => {
              await fetchCookieInfo();
              // 检查Cookie是否真的保存了
              const updatedCookieInfo = await fetch('/api/crawler/cookies', {
                headers: { 'Authorization': `Bearer ${token}` }
              }).then(res => res.json()).catch(() => null);

              if (updatedCookieInfo?.hasValidCookie) {
                alert('✅ 登录成功！Cookie已自动保存到数据库');
              } else {
                alert('⚠️ 登录成功，但Cookie保存可能有问题，请检查关键参数部分');
              }
            }, 1000);
          } else if (currentSession.status === 'expired') {
            alert('⏰ 二维码已过期，请重新创建登录会话');
          } else if (currentSession.status === 'failed') {
            alert('❌ 登录失败，请重试');
          }
        }, 2000);
      }
    }
  }, [sessions, currentSessionId, showQRModal]);

  const fetchSessions = async () => {
    try {
      if (!token) {
        console.log('⚠️ 未找到认证token，跳过获取会话');
        return;
      }

      const response = await fetch('/api/crawler/login', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.sessions && Array.isArray(data.sessions)) {
          setSessions(data.sessions.map((s: any) => s.session).filter(Boolean));
        } else {
          setSessions([]);
        }
      } else {
        const errorData = await response.json().catch(() => ({ error: '未知错误' }));
        console.error('获取登录会话失败:', response.status, errorData.error);
        setSessions([]);
      }
    } catch (error) {
      console.error('获取登录会话失败:', error);
      setSessions([]);
    }
  };

  const fetchCookieInfo = async () => {
    try {
      if (!token) {
        console.log('⚠️ 未找到认证token，跳过获取Cookie信息');
        return;
      }

      const response = await fetch('/api/crawler/cookies', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setCookieInfo(data);
      } else {
        const errorData = await response.json().catch(() => ({ error: '未知错误' }));
        console.error('获取Cookie信息失败:', response.status, errorData.error);
        setCookieInfo(null);
      }
    } catch (error) {
      console.error('获取Cookie信息失败:', error);
      setCookieInfo(null);
    }
  };

  const createLoginSession = async () => {
    setCreating(true);
    try {
      const response = await fetch('/api/crawler/login', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.qrcode) {
          setCurrentQRCode(data.qrcode);
          setCurrentSessionId(data.sessionId);
          setShowQRModal(true);
          fetchSessions();
        } else {
          alert('获取二维码失败');
        }
      } else {
        const data = await response.json();
        alert(data.error || '创建登录会话失败');
      }
    } catch (error) {
      console.error('创建登录会话失败:', error);
      alert('创建登录会话失败');
    } finally {
      setCreating(false);
    }
  };

  const handleEditValues = () => {
    setShowEditModal(true);
  };

  const handleSaveValues = async () => {
    try {
      const response = await fetch('/api/crawler/cookies', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editingValues)
      });

      if (response.ok) {
        setShowEditModal(false);
        await fetchCookieInfo(); // 刷新数据
        alert('保存成功！');
      } else {
        const errorData = await response.json().catch(() => ({ error: '未知错误' }));
        alert(`保存失败: ${errorData.error}`);
      }
    } catch (error) {
      console.error('保存失败:', error);
      alert('保存失败，请重试');
    }
  };

  const cancelSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/crawler/login?sessionId=${sessionId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        alert('登录会话已取消');
        fetchSessions();
      }
    } catch (error) {
      console.error('取消登录会话失败:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'scanned':
        return 'bg-blue-100 text-blue-800';
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '等待扫码';
      case 'scanned':
        return '已扫码';
      case 'confirmed':
        return '登录成功';
      case 'expired':
        return '已过期';
      case 'failed':
        return '登录失败';
      default:
        return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return '⏳';
      case 'scanned':
        return '👀';
      case 'confirmed':
        return '✅';
      case 'expired':
        return '⏰';
      case 'failed':
        return '❌';
      default:
        return '📱';
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">爬虫管理</h1>
          <p className="text-gray-600">管理微信公众号文章爬虫登录</p>
          {lastRefreshTime && (
            <p className="text-sm text-gray-500 mt-1">
              最后刷新: {lastRefreshTime.toLocaleTimeString()}
            </p>
          )}
        </div>
        <div className="flex space-x-3">
          <button
            onClick={async () => {
              setNetworkError('');
              try {
                await Promise.all([fetchSessions(), fetchCookieInfo()]);
                setLastRefreshTime(new Date());
              } catch (error) {
                setNetworkError('刷新失败，请检查网络连接');
              }
            }}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            🔄 刷新状态
          </button>
          <button
            onClick={createLoginSession}
            disabled={creating}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            {creating ? '创建中...' : '创建登录会话'}
          </button>
        </div>
      </div>

      {/* 网络错误提示 */}
      {networkError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="text-red-400 mr-3">⚠️</div>
            <div>
              <h3 className="text-sm font-medium text-red-800">网络连接问题</h3>
              <p className="text-sm text-red-700 mt-1">{networkError}</p>
              <button
                onClick={() => {
                  setNetworkError('');
                  fetchSessions();
                  fetchCookieInfo();
                }}
                className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
              >
                重试连接
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 说明卡片 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start">
          <div className="text-2xl mr-3">💡</div>
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">使用说明</h3>
            <div className="text-blue-800 space-y-2">
              <p>1. 点击"创建登录会话"按钮，系统会生成微信登录二维码</p>
              <p>2. 二维码会直接在页面弹窗中显示</p>
              <p>3. 使用微信扫描二维码完成登录</p>
              <p>4. 登录成功后，Cookie会自动保存，爬虫服务即可开始工作</p>
              <p>5. 登录会话有效期为5分钟</p>
            </div>
          </div>
        </div>
      </div>

      {/* 环境配置检查 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">环境配置</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">企业微信Webhook</span>
            <span className={`px-2 py-1 text-xs rounded-full ${
              process.env.NEXT_PUBLIC_WEWORK_WEBHOOK_CONFIGURED === 'true'
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {process.env.NEXT_PUBLIC_WEWORK_WEBHOOK_CONFIGURED === 'true' ? '已配置' : '未配置'}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-gray-600">微信登录凭证</span>
            <span className={`px-2 py-1 text-xs rounded-full ${
              cookieInfo?.hasValidCookie
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {cookieInfo?.hasValidCookie ? '已登录' : '未登录'}
            </span>
          </div>

          {cookieInfo?.hasValidCookie && (
            <div className="space-y-3">
              <div className="text-sm text-gray-500 space-y-1">
                <div>登录时间: {cookieInfo.loginTime ? new Date(cookieInfo.loginTime).toLocaleString() : '未知'}</div>
                <div>会话ID: {cookieInfo.sessionId || '未知'}</div>
                {cookieInfo.expiresAt && (
                  <div className="flex items-center space-x-2">
                    <span>过期时间:</span>
                    <span className={`${
                      new Date(cookieInfo.expiresAt) < new Date()
                        ? 'text-red-600 font-medium'
                        : new Date(cookieInfo.expiresAt).getTime() - Date.now() < 24 * 60 * 60 * 1000
                        ? 'text-orange-600 font-medium'
                        : 'text-green-600'
                    }`}>
                      {new Date(cookieInfo.expiresAt).toLocaleString()}
                      {new Date(cookieInfo.expiresAt) < new Date() && ' (已过期)'}
                      {new Date(cookieInfo.expiresAt) > new Date() &&
                       new Date(cookieInfo.expiresAt).getTime() - Date.now() < 24 * 60 * 60 * 1000 &&
                       ' (即将过期)'}
                    </span>
                  </div>
                )}
              </div>

              {/* 关键Cookie值显示 */}
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-700">关键参数</h4>
                  <div className="flex items-center space-x-2">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      cookieInfo?.hasValidCookie ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {cookieInfo?.hasValidCookie ? '完整' : '不完整'}
                    </span>
                    <button
                      onClick={handleEditValues}
                      className="text-xs text-blue-600 hover:text-blue-800 underline"
                    >
                      编辑
                    </button>
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-2 text-xs">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Token:</span>
                    <span className="font-mono text-gray-800 truncate ml-2" title={cookieInfo.wechat_token}>
                      {cookieInfo.wechat_token || '未设置'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">data_ticket:</span>
                    <span className="font-mono text-gray-800 truncate ml-2" title={cookieInfo.data_ticket}>
                      {cookieInfo.data_ticket || '未设置'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">rand_info:</span>
                    <span className="font-mono text-gray-800 truncate ml-2" title={cookieInfo.rand_info}>
                      {cookieInfo.rand_info || '未设置'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">bizuin:</span>
                    <span className="font-mono text-gray-800 truncate ml-2" title={cookieInfo.bizuin}>
                      {cookieInfo.bizuin || '未设置'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">slave_user:</span>
                    <span className="font-mono text-gray-800 truncate ml-2" title={cookieInfo.slave_user}>
                      {cookieInfo.slave_user || '未设置'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">slave_sid:</span>
                    <span className="font-mono text-gray-800 truncate ml-2" title={cookieInfo.slave_sid}>
                      {cookieInfo.slave_sid || '未设置'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="text-sm text-gray-500">
            需要先完成微信登录才能开始爬取文章
          </div>
        </div>
      </div>

      {/* 登录会话列表 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">活跃登录会话</h3>
        </div>
        
        {loading ? (
          <div className="p-8 text-center text-gray-500">加载中...</div>
        ) : sessions.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <div className="text-4xl mb-4">📱</div>
            <p>暂无活跃的登录会话</p>
            <p className="text-sm mt-2">点击上方按钮创建新的登录会话</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {sessions.map((session) => (
              <div key={session.id} className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">
                      {getStatusIcon(session.status)}
                    </div>
                    <div>
                      <div className="flex items-center space-x-3">
                        <h4 className="text-sm font-medium text-gray-900">
                          会话 {session.id.slice(0, 8)}...
                        </h4>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(session.status)}`}>
                          {getStatusText(session.status)}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        创建时间: {new Date(session.createdAt).toLocaleString()}
                      </div>
                      {session.userInfo && (
                        <div className="flex items-center space-x-2 mt-2">
                          <img
                            src={session.userInfo.avatar}
                            alt={session.userInfo.nickname}
                            className="w-6 h-6 rounded-full"
                          />
                          <span className="text-sm text-gray-700">
                            {session.userInfo.nickname}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {session.status === 'pending' && (
                      <button
                        onClick={() => cancelSession(session.id)}
                        className="px-3 py-1 text-sm text-red-600 hover:text-red-900"
                      >
                        取消
                      </button>
                    )}
                    <div className="text-xs text-gray-400">
                      {session.status === 'pending' && (
                        <>过期时间: {new Date(session.expiresAt).toLocaleString()}</>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 二维码显示模态框 */}
      {showQRModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-4">微信扫码登录</h3>

              {/* 二维码显示 */}
              <div className="flex justify-center mb-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <img
                    src={currentQRCode}
                    alt="微信登录二维码"
                    className="w-64 h-64 object-contain"
                  />
                </div>
              </div>

              {/* 说明文字 */}
              <div className="text-sm text-gray-600 mb-4 space-y-2">
                <p>请使用微信扫描上方二维码</p>
                <p>会话ID: <code className="bg-gray-100 px-2 py-1 rounded text-xs">{currentSessionId}</code></p>
                <p className="text-xs text-gray-500">二维码有效期：5分钟</p>
              </div>

              {/* 状态显示 */}
              <div className="mb-4">
                {sessions.find(s => s.id === currentSessionId) && (
                  <div className="text-sm space-y-2">
                    <div className="flex items-center justify-center space-x-2">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        getStatusColor(sessions.find(s => s.id === currentSessionId)?.status || 'pending')
                      }`}>
                        {getStatusText(sessions.find(s => s.id === currentSessionId)?.status || 'pending')}
                      </span>
                      {isPolling && (
                        <div className="flex items-center text-xs text-gray-500">
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500 mr-1"></div>
                          自动检查中...
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* 按钮 */}
              <div className="flex justify-center space-x-3">
                <button
                  onClick={() => {
                    setShowQRModal(false);
                    setCurrentQRCode('');
                    setCurrentSessionId('');
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
                >
                  关闭
                </button>
                <button
                  onClick={() => {
                    fetchSessions();
                  }}
                  className="px-4 py-2 text-blue-600 bg-blue-100 rounded-lg hover:bg-blue-200"
                >
                  刷新状态
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 使用提示 */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">注意事项</h3>
        <div className="text-gray-700 space-y-2 text-sm">
          <p>• 每个登录会话有效期为5分钟，过期后需要重新创建</p>
          <p>• 登录成功后，爬虫服务可以访问您的微信公众号文章</p>
          <p>• 建议定期检查登录状态，确保爬虫服务正常运行</p>
          <p>• 如果登录失败，请检查网络连接和微信账号状态</p>
        </div>
      </div>

      {/* 编辑关键参数模态框 */}
      {showEditModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">编辑关键参数</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Token</label>
                <input
                  type="text"
                  value={editingValues.wechat_token}
                  onChange={(e) => setEditingValues({...editingValues, wechat_token: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入微信token"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">data_ticket</label>
                <input
                  type="text"
                  value={editingValues.data_ticket}
                  onChange={(e) => setEditingValues({...editingValues, data_ticket: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入data_ticket"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">rand_info</label>
                <input
                  type="text"
                  value={editingValues.rand_info}
                  onChange={(e) => setEditingValues({...editingValues, rand_info: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入rand_info"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">bizuin</label>
                <input
                  type="text"
                  value={editingValues.bizuin}
                  onChange={(e) => setEditingValues({...editingValues, bizuin: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入bizuin"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">slave_user</label>
                <input
                  type="text"
                  value={editingValues.slave_user}
                  onChange={(e) => setEditingValues({...editingValues, slave_user: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入slave_user"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">slave_sid</label>
                <input
                  type="text"
                  value={editingValues.slave_sid}
                  onChange={(e) => setEditingValues({...editingValues, slave_sid: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入slave_sid"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                取消
              </button>
              <button
                onClick={handleSaveValues}
                className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                保存
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
