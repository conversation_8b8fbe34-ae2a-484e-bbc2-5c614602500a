name: Release Docker Image

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string

env:
  REGISTRY: registry.cn-hangzhou.aliyuncs.com
  NAMESPACE: feedwe
  IMAGE_NAME: crawler

jobs:
  release:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Aliyun Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
        password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}
    
    - name: Get version
      id: version
      run: |
        if [ "${{ github.event_name }}" = "release" ]; then
          echo "version=${{ github.event.release.tag_name }}" >> $GITHUB_OUTPUT
        else
          echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
        fi
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/${{ env.IMAGE_NAME }}
        tags: |
          type=raw,value=${{ steps.version.outputs.version }}
          type=raw,value=stable
          type=raw,value=latest
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.crawler
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        platforms: linux/amd64,linux/arm64
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Create release notes
      run: |
        cat > release-notes.md << EOF
        # FeedWe Crawler Docker Image Release ${{ steps.version.outputs.version }}
        
        ## 🐳 Docker Image
        \`\`\`bash
        docker pull ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}
        \`\`\`
        
        ## 🚀 Quick Deploy
        \`\`\`bash
        # 更新 docker-compose.crawler.yml 中的镜像标签
        image: ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.version }}
        
        # 部署
        docker-compose -f docker-compose.crawler.yml up -d
        \`\`\`
        
        ## 📋 Build Information
        - **Build Time**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        - **Commit SHA**: ${{ github.sha }}
        - **Platforms**: linux/amd64, linux/arm64
        
        ## 🔗 Registry Links
        - [阿里云镜像仓库](${{ env.REGISTRY }}/${{ env.NAMESPACE }}/${{ env.IMAGE_NAME }})
        EOF
        
    - name: Upload release notes
      uses: actions/upload-artifact@v4
      with:
        name: release-notes
        path: release-notes.md
