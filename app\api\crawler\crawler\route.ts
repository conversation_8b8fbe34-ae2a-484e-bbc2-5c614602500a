import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../lib/auth/admin';
import { WechatCrawler } from '../../../../lib/wechat-crawler';
import { WeWorkWebhook } from '../../../../lib/wework-webhook';
import { prisma } from '../../../../lib/prisma';

// 存储活跃的爬虫任务
const activeTasks = new Map<string, any>();

// 开始爬取任务
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const { accountName, fakeId, sessionId, limit = 50 } = await request.json();

    if (!accountName || !fakeId || !sessionId) {
      return NextResponse.json(
        { error: '缺少必要参数：accountName, fakeId, sessionId' },
        { status: 400 }
      );
    }

    // 从数据库获取微信登录相关配置
    const configNames = [
      'wechat_article_token',
      'wechat_article_cookie_string',
      'wechat_article_data_ticket',
      'wechat_article_rand_info',
      'wechat_article_bizuin',
      'wechat_article_slave_sid',
      'wechat_article_slave_user'
    ];

    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: configNames
        }
      }
    });

    const configMap: Record<string, string> = {};
    configs.forEach(config => {
      configMap[config.name] = config.value;
    });

    // 检查必要的配置
    if (!configMap.wechat_article_token || !configMap.wechat_article_cookie_string) {
      return NextResponse.json(
        { error: '未找到有效的登录凭证，请先完成微信登录' },
        { status: 401 }
      );
    }

    const cookieString = configMap.wechat_article_cookie_string;
    const wechatToken = configMap.wechat_article_token;

    const taskId = `crawl_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

    // 初始化企业微信通知
    let webhook: WeWorkWebhook | null = null;
    try {
      const webhookUrl = process.env.WEWORK_WEBHOOK_URL;
      if (webhookUrl && !webhookUrl.includes('YOUR_WEBHOOK_KEY')) {
        webhook = new WeWorkWebhook(webhookUrl);
      }
    } catch (e) {
      console.log('企业微信Webhook未配置');
    }

    // 发送开始通知
    if (webhook) {
      await webhook.sendCrawlerNotification('start', {
        accountName,
        taskId
      });
    }

    // 创建爬虫实例
    const crawler = new WechatCrawler({
      cookieString: cookieString,
      token: wechatToken,
      onProgress: async (progress) => {
        console.log(`爬取进度: ${progress.current}/${progress.total}`);
        // 可以在这里更新任务状态
      },
      onComplete: async (articles) => {
        console.log(`爬取完成，共获取 ${articles.length} 篇文章`);
        
        // 保存文章到数据库
        try {
          await saveArticlesToDatabase(accountName, articles);
          
          // 发送成功通知
          if (webhook) {
            await webhook.sendCrawlerNotification('success', {
              accountName,
              taskId,
              articleCount: articles.length
            });
          }
        } catch (error) {
          console.error('保存文章失败:', error);
          if (webhook) {
            await webhook.sendCrawlerNotification('error', {
              accountName,
              taskId,
              error: error instanceof Error ? error.message : '保存文章失败'
            });
          }
        }
        
        // 清理任务
        activeTasks.delete(taskId);
      },
      onError: async (error) => {
        console.error('爬取失败:', error);
        
        // 发送失败通知
        if (webhook) {
          await webhook.sendCrawlerNotification('error', {
            accountName,
            taskId,
            error: error.message
          });
        }
        
        // 清理任务
        activeTasks.delete(taskId);
      }
    });

    // 保存任务信息
    activeTasks.set(taskId, {
      taskId,
      accountName,
      sessionId,
      status: 'running',
      startTime: new Date(),
      crawler
    });

    // 异步开始爬取
    crawler.crawlArticles(fakeId, accountName, limit).catch(error => {
      console.error('爬取任务异常:', error);
    });

    return NextResponse.json({
      success: true,
      taskId,
      message: '爬取任务已开始'
    });
  } catch (error) {
    console.error('创建爬取任务失败:', error);
    const errorMessage = error instanceof Error ? error.message : '创建爬取任务失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 获取爬取任务状态
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');

    if (taskId) {
      // 返回特定任务状态
      const task = activeTasks.get(taskId);
      if (!task) {
        return NextResponse.json(
          { error: '任务不存在或已完成' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        task: {
          taskId: task.taskId,
          accountName: task.accountName,
          status: task.status,
          startTime: task.startTime
        }
      });
    } else {
      // 返回所有活跃任务
      const tasks = Array.from(activeTasks.values()).map(task => ({
        taskId: task.taskId,
        accountName: task.accountName,
        status: task.status,
        startTime: task.startTime
      }));

      return NextResponse.json({
        success: true,
        tasks
      });
    }
  } catch (error) {
    console.error('获取爬取任务状态失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取任务状态失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 保存文章到数据库
async function saveArticlesToDatabase(accountName: string, articles: any[]) {
  try {
    // 查找或创建公众号
    let wechatAccount = await prisma.wechatAccount.findFirst({
      where: { name: accountName }
    });

    if (!wechatAccount) {
      wechatAccount = await prisma.wechatAccount.create({
        data: {
          name: accountName,
          avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
          description: `${accountName}的微信公众号`
        }
      });
    }

    // 保存文章
    for (const article of articles) {
      // 检查文章是否已存在
      const existingArticle = await prisma.article.findFirst({
        where: {
          title: article.title,
          wechatAccountId: wechatAccount.id
        }
      });

      if (!existingArticle) {
        await prisma.article.create({
          data: {
            title: article.title,
            url: article.url,
            publishDate: new Date(article.publishTime),
            summary: article.digest,
            wechatAccountId: wechatAccount.id
          }
        });
      }
    }

    console.log(`✅ 成功保存 ${articles.length} 篇文章到数据库`);
  } catch (error) {
    console.error('保存文章到数据库失败:', error);
    throw error;
  }
}
