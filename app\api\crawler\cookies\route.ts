import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../lib/auth/admin';
import { prisma } from '../../../../lib/prisma';

// 保存登录Cookie到数据库
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const { sessionId, cookieParams, token: wechatToken } = await request.json();

    if (!sessionId || !cookieParams || !wechatToken) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 构建Cookie字符串
    const cookieString = `data_ticket=${cookieParams.data_ticket || ''};rand_info=${cookieParams.rand_info || ''};bizuin=${cookieParams.bizuin || ''};slave_sid=${cookieParams.slave_sid || ''};slave_user=${cookieParams.slave_user || ''}`;

    // 保存到数据库（使用系统配置表）
    const configs = [
      { name: 'wechat_article_data_ticket', value: cookieParams.data_ticket || '' },
      { name: 'wechat_article_rand_info', value: cookieParams.rand_info || '' },
      { name: 'wechat_article_bizuin', value: cookieParams.bizuin || '' },
      { name: 'wechat_article_slave_sid', value: cookieParams.slave_sid || '' },
      { name: 'wechat_article_slave_user', value: cookieParams.slave_user || '' },
      { name: 'wechat_article_token', value: wechatToken },
      { name: 'wechat_article_session_id', value: sessionId },
      { name: 'wechat_article_login_time', value: new Date().toISOString() }
    ];

    // 使用upsert更新或创建配置
    for (const config of configs) {
      await prisma.systemConfig.upsert({
        where: { name: config.name },
        update: { 
          value: config.value,
          updatedAt: new Date()
        },
        create: {
          name: config.name,
          value: config.value
        }
      });
    }

    console.log('✅ 微信登录Cookie已保存到数据库');

    return NextResponse.json({
      success: true,
      message: '登录凭证已保存',
      cookieString: cookieString.substring(0, 50) + '...'
    });
  } catch (error) {
    console.error('保存Cookie失败:', error);
    const errorMessage = error instanceof Error ? error.message : '保存Cookie失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 获取当前保存的Cookie信息
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    // 从数据库获取配置
    const configNames = [
      'wechat_article_data_ticket',
      'wechat_article_rand_info',
      'wechat_article_bizuin',
      'wechat_article_slave_sid',
      'wechat_article_slave_user',
      'wechat_article_token',
      'wechat_article_cookie_string',
      'wechat_article_session_id',
      'wechat_article_login_time'
    ];

    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: configNames
        }
      }
    });

    const configMap: Record<string, string> = {};
    configs.forEach(config => {
      configMap[config.name] = config.value;
    });

    // 调试日志：显示所有配置
    console.log('🔍 数据库中的配置:', Object.keys(configMap));
    console.log('🔑 Token配置:', {
      wechat_article_token: configMap.wechat_article_token ? '已设置' : '未设置'
    });

    const hasValidCookie = !!(
      configMap.wechat_article_data_ticket &&
      configMap.wechat_article_token &&
      configMap.wechat_article_cookie_string
    );

    // 计算过期时间（4天后）
    const loginTime = configMap.wechat_article_login_time;
    let expiresAt = null;
    if (loginTime) {
      const loginDate = new Date(loginTime);
      expiresAt = new Date(loginDate.getTime() + 4 * 24 * 60 * 60 * 1000); // 4天后
    }

    return NextResponse.json({
      success: true,
      hasValidCookie,
      loginTime: configMap.wechat_article_login_time,
      expiresAt: expiresAt?.toISOString(),
      sessionId: configMap.wechat_article_session_id,
      cookiePreview: configMap.wechat_article_cookie_string?.substring(0, 50) + '...',
      // 直接返回关键参数的值
      wechat_token: configMap.wechat_article_token || '',
      data_ticket: configMap.wechat_article_data_ticket || '',
      rand_info: configMap.wechat_article_rand_info || '',
      bizuin: configMap.wechat_article_bizuin || '',
      slave_sid: configMap.wechat_article_slave_sid || '',
      slave_user: configMap.wechat_article_slave_user || '',
      // 保持向后兼容
      cookieParams: {
        data_ticket: configMap.wechat_article_data_ticket ? '已设置' : '未设置',
        rand_info: configMap.wechat_article_rand_info ? '已设置' : '未设置',
        bizuin: configMap.wechat_article_bizuin ? '已设置' : '未设置',
        slave_sid: configMap.wechat_article_slave_sid ? '已设置' : '未设置',
        slave_user: configMap.wechat_article_slave_user ? '已设置' : '未设置'
      }
    });
  } catch (error) {
    console.error('获取Cookie信息失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取Cookie信息失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 清除保存的Cookie
export async function DELETE(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    // 删除相关配置
    const configNames = [
      'wechat_article_data_ticket',
      'wechat_article_rand_info',
      'wechat_article_bizuin',
      'wechat_article_slave_sid',
      'wechat_article_slave_user',
      'wechat_article_token',
      'wechat_article_cookie_string',
      'wechat_article_session_id',
      'wechat_article_login_time'
    ];

    await prisma.systemConfig.deleteMany({
      where: {
        name: {
          in: configNames
        }
      }
    });

    console.log('✅ 微信登录Cookie已清除');

    return NextResponse.json({
      success: true,
      message: '登录凭证已清除'
    });
  } catch (error) {
    console.error('清除Cookie失败:', error);
    const errorMessage = error instanceof Error ? error.message : '清除Cookie失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 更新关键参数
export async function PUT(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);

    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const {
      wechat_token,
      data_ticket,
      rand_info,
      bizuin,
      slave_user,
      slave_sid
    } = await request.json();

    console.log('🔄 更新关键参数:', {
      wechat_token: wechat_token ? '已设置' : '未设置',
      data_ticket: data_ticket ? '已设置' : '未设置',
      rand_info: rand_info ? '已设置' : '未设置',
      bizuin: bizuin ? '已设置' : '未设置',
      slave_user: slave_user ? '已设置' : '未设置',
      slave_sid: slave_sid ? '已设置' : '未设置'
    });

    // 更新各个参数到数据库
    const updates = [
      { name: 'wechat_article_token', value: wechat_token || '' },
      { name: 'wechat_article_data_ticket', value: data_ticket || '' },
      { name: 'wechat_article_rand_info', value: rand_info || '' },
      { name: 'wechat_article_bizuin', value: bizuin || '' },
      { name: 'wechat_article_slave_user', value: slave_user || '' },
      { name: 'wechat_article_slave_sid', value: slave_sid || '' }
    ];

    // 批量更新
    for (const update of updates) {
      await prisma.systemConfig.upsert({
        where: { name: update.name },
        update: { value: update.value },
        create: { name: update.name, value: update.value }
      });
    }

    console.log('✅ 关键参数更新成功');

    return NextResponse.json({
      success: true,
      message: '关键参数更新成功'
    });

  } catch (error) {
    console.error('❌ 更新关键参数失败:', error);
    return NextResponse.json({ error: '更新关键参数失败' }, { status: 500 });
  }
}
