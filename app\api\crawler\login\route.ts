import { NextRequest, NextResponse } from 'next/server';
import { WechatCrawlerLogin } from '../../../../lib/wechat-crawler-login';
import { WeWorkWebhook } from '../../../../lib/wework-webhook';
import { verifyToken } from '../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../lib/auth/admin';
import { prisma } from '../../../../lib/prisma';

// 存储活跃的登录会话
const activeSessions = new Map<string, WechatCrawlerLogin>();

// 初始化企业微信Webhook
const getWeWorkWebhook = () => {
  const webhookUrl = process.env.WEWORK_WEBHOOK_URL;
  if (!webhookUrl) {
    throw new Error('未配置企业微信Webhook URL');
  }
  return new WeWorkWebhook(webhookUrl);
};

// 开始微信登录
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    // 检查是否启用模拟模式（用于测试）
    const mockMode = process.env.WECHAT_LOGIN_MOCK_MODE === 'true';
    console.log('🎭 模拟模式:', mockMode ? '启用' : '禁用');

    // 创建新的登录会话
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode: string) => {
        // 不再发送到企业微信，二维码将通过API返回给前端显示
        console.log('📱 二维码已生成，准备返回给前端显示');
      },
      onStatusChange: async (status: string, data?: any) => {
        const session = wechatLogin.getSession();
        if (session) {
          console.log(`📊 登录状态更新: ${status}`, data);
        }
      },
      onSuccess: async (userInfo: any, token: string) => {
        console.log('微信登录成功:', userInfo, 'token:', token);
        // Cookie已在登录过程中自动保存到数据库
      },
      onError: async (error: Error) => {
        console.error('微信登录失败:', error);
      }
    });

    // 开始登录流程
    const qrcode = await wechatLogin.startLogin();

    // 获取会话信息
    const session = wechatLogin.getSession();
    if (!session) {
      throw new Error('创建登录会话失败');
    }

    const sessionId = session.id;

    // 保存会话
    activeSessions.set(sessionId, wechatLogin);

    // 设置会话过期清理
    setTimeout(() => {
      activeSessions.delete(sessionId);
    }, 5 * 60 * 1000); // 5分钟后清理

    console.log('✅ 登录会话创建成功:', {
      sessionId,
      hasQRCode: !!qrcode,
      qrcodeLength: qrcode?.length || 0
    });

    return NextResponse.json({
      success: true,
      sessionId,
      qrcode,
      message: '登录会话已创建'
    });
  } catch (error) {
    console.error('创建微信登录会话失败:', error);
    const errorMessage = error instanceof Error ? error.message : '创建登录会话失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 获取登录状态
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      // 返回所有活跃会话
      const sessions = Array.from(activeSessions.entries()).map(([id, login]) => ({
        id,
        session: login.getSession()
      }));

      return NextResponse.json({
        success: true,
        sessions
      });
    }

    // 返回特定会话状态
    const wechatLogin = activeSessions.get(sessionId);
    if (!wechatLogin) {
      return NextResponse.json(
        { error: '会话不存在或已过期' },
        { status: 404 }
      );
    }

    const session = wechatLogin.getSession();
    return NextResponse.json({
      success: true,
      session
    });
  } catch (error) {
    console.error('获取登录状态失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取登录状态失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 取消登录
export async function DELETE(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: '缺少会话ID' },
        { status: 400 }
      );
    }

    const wechatLogin = activeSessions.get(sessionId);
    if (!wechatLogin) {
      return NextResponse.json(
        { error: '会话不存在或已过期' },
        { status: 404 }
      );
    }

    // 取消登录
    wechatLogin.stopLogin();
    activeSessions.delete(sessionId);

    // 通知企业微信
    const webhook = getWeWorkWebhook();
    await webhook.sendLoginStatusUpdate(sessionId, 'cancelled');

    return NextResponse.json({
      success: true,
      message: '登录会话已取消'
    });
  } catch (error) {
    console.error('取消登录失败:', error);
    const errorMessage = error instanceof Error ? error.message : '取消登录失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 保存Cookie到数据库的辅助函数
async function saveCookiesToDatabase(sessionId: string, cookieParams: any, wechatToken: string) {
  try {
    // 构建Cookie字符串
    const cookieString = `data_ticket=${cookieParams.data_ticket || ''};rand_info=${cookieParams.rand_info || ''};bizuin=${cookieParams.bizuin || ''};slave_sid=${cookieParams.slave_sid || ''};slave_user=${cookieParams.slave_user || ''}`;

    // 保存到数据库的配置项
    const configs = [
      { name: 'wechat_article_data_ticket', value: cookieParams.data_ticket || '' },
      { name: 'wechat_article_rand_info', value: cookieParams.rand_info || '' },
      { name: 'wechat_article_bizuin', value: cookieParams.bizuin || '' },
      { name: 'wechat_article_slave_sid', value: cookieParams.slave_sid || '' },
      { name: 'wechat_article_slave_user', value: cookieParams.slave_user || '' },
      { name: 'wechat_article_token', value: wechatToken },
      { name: 'wechat_article_cookie_string', value: cookieString },
      { name: 'wechat_article_session_id', value: sessionId },
      { name: 'wechat_article_login_time', value: new Date().toISOString() }
    ];

    // 使用upsert更新或创建配置
    for (const config of configs) {
      await prisma.systemConfig.upsert({
        where: { name: config.name },
        update: {
          value: config.value,
          updatedAt: new Date()
        },
        create: {
          name: config.name,
          value: config.value
        }
      });
    }

    console.log('✅ 微信登录Cookie已保存到数据库');
    return true;
  } catch (error) {
    console.error('❌ 保存Cookie到数据库失败:', error);
    throw error;
  }
}

// PUT方法：保存Cookie到数据库
export async function PUT(request: NextRequest) {
  try {
    const { sessionId, cookies, token } = await request.json();

    if (!sessionId || !cookies) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数'
      }, { status: 400 });
    }

    console.log('💾 收到Cookie保存请求:', {
      sessionId,
      cookieCount: Object.keys(cookies).length,
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 8)}...` : 'none'
    });

    // 使用从redirect_url提取的真实token，如果没有则使用默认值
    const realToken = token || 'real_login_token';
    console.log('🔑 使用token:', realToken === token ? '从redirect_url提取的真实token' : '默认token');

    // 保存Cookie到数据库
    await saveCookiesToDatabase(sessionId, cookies, realToken);

    return NextResponse.json({
      success: true,
      message: 'Cookie保存成功'
    });

  } catch (error) {
    console.error('保存Cookie失败:', error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : '保存Cookie失败'
    }, { status: 500 });
  }
}
