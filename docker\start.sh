#!/bin/bash

# FeedWe 定时任务爬虫服务启动脚本

set -e

echo "🚀 启动 FeedWe 定时任务爬虫服务..."

# 检查环境变量
if [ -z "$DATABASE_URL" ]; then
    echo "❌ 错误: DATABASE_URL 环境变量未设置"
    exit 1
fi

if [ -z "$WECHAT_ARTICLE_COOKIE_STRING" ]; then
    echo "⚠️ 警告: WECHAT_ARTICLE_COOKIE_STRING 环境变量未设置"
fi

if [ -z "$WECHAT_ARTICLE_TOKEN" ]; then
    echo "⚠️ 警告: WECHAT_ARTICLE_TOKEN 环境变量未设置"
fi

# 等待数据库就绪
echo "🔍 检查数据库连接..."
npx prisma db push --accept-data-loss || {
    echo "❌ 数据库连接失败"
    exit 1
}

echo "✅ 数据库连接正常"

# 生成 Prisma 客户端
echo "🔧 生成 Prisma 客户端..."
npx prisma generate

# 启动服务
echo "🎯 启动定时任务服务..."
exec node docker/crawler-service.js
