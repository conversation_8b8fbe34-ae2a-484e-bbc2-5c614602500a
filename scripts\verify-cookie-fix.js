#!/usr/bin/env node

/**
 * 验证Cookie保存修复的脚本
 */

async function verifyCookieFix() {
  console.log('🔍 验证Cookie保存修复...');
  
  try {
    // 检查数据库中的Cookie配置
    const { prisma } = await import('../lib/prisma');
    
    const configNames = [
      'wechat_article_data_ticket',
      'wechat_article_rand_info',
      'wechat_article_bizuin',
      'wechat_article_slave_sid',
      'wechat_article_slave_user',
      'wechat_article_token',
      'wechat_article_cookie_string',
      'wechat_article_session_id',
      'wechat_article_login_time'
    ];

    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: configNames
        }
      }
    });

    console.log('📊 数据库检查结果:');
    console.log(`  找到配置项: ${configs.length}/${configNames.length}`);
    
    const configMap = {};
    configs.forEach(config => {
      configMap[config.name] = config.value;
    });

    // 检查每个配置的状态
    let validCount = 0;
    for (const name of configNames) {
      const value = configMap[name];
      const hasValue = value && value.length > 0 && value !== '';
      const status = hasValue ? '✅' : '❌';
      
      if (hasValue) validCount++;
      
      console.log(`  ${name}: ${status}`);
      if (hasValue && name === 'wechat_article_login_time') {
        const loginTime = new Date(value);
        const now = new Date();
        const diffHours = (now - loginTime) / (1000 * 60 * 60);
        console.log(`    登录时间: ${loginTime.toLocaleString()} (${diffHours.toFixed(1)}小时前)`);
      }
    }

    // 总体评估
    console.log('\n📋 总体状态:');
    if (validCount === 0) {
      console.log('❌ 没有找到任何Cookie配置，可能还未进行过登录');
    } else if (validCount < 6) {
      console.log(`⚠️ Cookie配置不完整 (${validCount}/${configNames.length})，可能存在保存问题`);
    } else {
      console.log(`✅ Cookie配置完整 (${validCount}/${configNames.length})，保存功能正常`);
    }

    // 检查关键字段
    const criticalFields = ['wechat_article_token', 'wechat_article_data_ticket', 'wechat_article_cookie_string'];
    const hasCriticalFields = criticalFields.every(field => configMap[field] && configMap[field].length > 0);
    
    console.log('\n🔑 关键字段检查:');
    console.log(`  状态: ${hasCriticalFields ? '✅ 完整' : '❌ 缺失'}`);
    
    if (hasCriticalFields) {
      console.log('  Token长度:', configMap.wechat_article_token?.length || 0);
      console.log('  Cookie字符串长度:', configMap.wechat_article_cookie_string?.length || 0);
      console.log('  data_ticket长度:', configMap.wechat_article_data_ticket?.length || 0);
    }

    await prisma.$disconnect();

    // 检查代码修复
    console.log('\n🔧 代码修复检查:');
    const fs = await import('fs');
    const path = await import('path');
    
    const loginFilePath = path.join(process.cwd(), 'lib', 'wechat-crawler-login.ts');
    if (fs.existsSync(loginFilePath)) {
      const content = fs.readFileSync(loginFilePath, 'utf8');
      
      // 检查是否包含修复的代码
      const hasFixedUrl = content.includes('window.location.origin') && content.includes('VERCEL_URL');
      const hasBackupMethod = content.includes('saveCookiesDirectly');
      const hasErrorHandling = content.includes('网络调用失败，尝试直接保存');
      
      console.log('  动态URL获取:', hasFixedUrl ? '✅' : '❌');
      console.log('  备用保存方法:', hasBackupMethod ? '✅' : '❌');
      console.log('  错误处理增强:', hasErrorHandling ? '✅' : '❌');
      
      if (hasFixedUrl && hasBackupMethod && hasErrorHandling) {
        console.log('✅ 代码修复已正确应用');
      } else {
        console.log('❌ 代码修复可能不完整');
      }
    } else {
      console.log('❌ 无法找到登录文件');
    }

    // 提供建议
    console.log('\n💡 建议:');
    if (validCount === 0) {
      console.log('1. 访问 /admin/crawler 页面进行微信登录测试');
      console.log('2. 扫码登录后检查Cookie是否正确保存');
    } else if (!hasCriticalFields) {
      console.log('1. 重新进行微信登录以获取完整的Cookie');
      console.log('2. 检查网络连接和API调用是否正常');
    } else {
      console.log('1. Cookie保存功能正常，可以开始使用爬虫功能');
      console.log('2. 建议定期检查Cookie有效性');
    }

  } catch (error) {
    console.error('❌ 验证过程失败:', error);
    process.exit(1);
  }
}

// 运行验证
verifyCookieFix();
