name: Build and Push Docker Image

on:
  push:
    branches: [ main, master ]
    paths:
      - 'Dockerfile.crawler'
      - 'docker/**'
      - 'lib/**'
      - 'prisma/**'
      - 'package.json'
      - 'package-lock.json'
  pull_request:
    branches: [ main, master ]
    paths:
      - 'Dockerfile.crawler'
      - 'docker/**'
  workflow_dispatch:
    inputs:
      tag:
        description: 'Docker image tag'
        required: false
        default: 'latest'

env:
  # 阿里云镜像仓库配置
  REGISTRY: registry.cn-hangzhou.aliyuncs.com
  NAMESPACE: feedwe
  IMAGE_NAME: crawler

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Aliyun Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
        password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          type=raw,value=${{ github.event.inputs.tag }},enable=${{ github.event_name == 'workflow_dispatch' }}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.crawler
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        platforms: linux/amd64,linux/arm64
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Image digest
      run: echo ${{ steps.build.outputs.digest }}
      
    - name: Update deployment files
      if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
      run: |
        # 更新 docker-compose 文件中的镜像标签
        sed -i "s|image:.*|image: ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/${{ env.IMAGE_NAME }}:latest|g" docker-compose.crawler.yml || true
        
        # 创建部署信息文件
        cat > deployment-info.txt << EOF
        Docker Image: ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/${{ env.IMAGE_NAME }}:latest
        Build Time: $(date -u +"%Y-%m-%dT%H:%M:%SZ")
        Commit SHA: ${{ github.sha }}
        Branch: ${{ github.ref_name }}
        EOF
        
    - name: Commit updated files
      if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add docker-compose.crawler.yml deployment-info.txt || true
        git diff --staged --quiet || git commit -m "chore: update docker image tag to latest [skip ci]" || true
        git push || true
