import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';
import { WechatCrawlerLogin } from '../../../../lib/wechat-crawler-login';
import { WebhookNotificationService } from '../../../../lib/webhook-notification';

// Vercel Cron Job 端点
export async function GET(request: NextRequest) {
  // 验证请求来源（Vercel Cron会发送特殊的header）
  const authHeader = request.headers.get('authorization');
  const cronSecret = process.env.CRON_SECRET;
  
  if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  console.log('🚀 Vercel Cron: 开始执行文章爬取任务');
  
  try {
    // 获取当前时间，检查是否在允许执行的时间范围内
    const now = new Date();
    const hour = now.getHours();
    
    // 只在6:00-23:00之间执行（避免夜间频繁执行）
    if (hour < 6 || hour >= 23) {
      console.log(`⏰ 当前时间 ${hour}:00 不在执行范围内，跳过执行`);
      return NextResponse.json({ 
        success: true, 
        message: '不在执行时间范围内',
        currentHour: hour 
      });
    }

    // 获取30分钟前的时间（冷却期检查）
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    
    // 获取所有启用爬取的公众号
    const allEnabledAccounts = await prisma.wechatAccount.findMany({
      where: {
        enableCrawling: true,
        fakeid: { not: null }
      },
      include: {
        articles: {
          orderBy: {
            publishDate: 'desc'
          },
          take: 1
        }
      }
    });

    // 过滤出符合30分钟冷却期条件的公众号
    const eligibleAccounts = allEnabledAccounts.filter(account => {
      if (account.articles.length === 0) {
        return true;
      }
      
      const latestArticle = account.articles[0];
      const timeDiff = Date.now() - latestArticle.publishDate.getTime();
      const minutesAgo = Math.floor(timeDiff / (1000 * 60));
      
      return minutesAgo >= 30;
    });

    console.log(`📊 找到 ${eligibleAccounts.length} 个可执行爬取的公众号`);
    
    if (eligibleAccounts.length === 0) {
      return NextResponse.json({ 
        success: true, 
        message: '没有需要爬取的公众号',
        totalAccounts: allEnabledAccounts.length,
        eligibleAccounts: 0
      });
    }

    // 获取微信凭证
    const credentials = await getWechatCredentials();
    if (!credentials) {
      console.error('❌ 微信凭证不完整');
      return NextResponse.json({
        error: '微信凭证不完整，请先完成微信登录',
        needLogin: true
      }, { status: 500 });
    }

    let totalNewArticles = 0;
    const results = [];

    // 处理每个公众号（限制处理数量避免超时）
    const maxAccountsPerRun = 3; // 每次最多处理3个公众号
    const accountsToProcess = eligibleAccounts.slice(0, maxAccountsPerRun);

    for (const account of accountsToProcess) {
      try {
        console.log(`🔍 开始爬取公众号: ${account.name}`);

        const articles = await fetchWechatArticles(
          account.fakeid!,
          credentials
        );

        if (articles && articles.length > 0) {
          const newArticlesCount = await saveNewArticles(account.id, articles);
          totalNewArticles += newArticlesCount;
          
          results.push({
            accountName: account.name,
            newArticles: newArticlesCount,
            totalArticles: articles.length
          });
          
          console.log(`✅ 公众号 ${account.name} 爬取完成，新增 ${newArticlesCount} 篇文章`);
        } else {
          results.push({
            accountName: account.name,
            newArticles: 0,
            error: '未获取到文章数据'
          });
        }
      } catch (error) {
        console.error(`❌ 爬取公众号 ${account.name} 失败:`, error);
        results.push({
          accountName: account.name,
          newArticles: 0,
          error: error instanceof Error ? error.message : '未知错误'
        });
      }
    }

    console.log(`🎉 Vercel Cron: 爬取任务完成，总共新增 ${totalNewArticles} 篇文章`);

    return NextResponse.json({
      success: true,
      message: 'Cron任务执行完成',
      totalNewArticles,
      processedAccounts: accountsToProcess.length,
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Vercel Cron: 执行失败:', error);
    return NextResponse.json({
      error: 'Cron任务执行失败',
      details: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// 获取微信登录凭证
async function getWechatCredentials(): Promise<{
  token: string;
  cookie: string;
  dataTicket: string;
} | null> {
  try {
    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: [
            'wechat_article_token',
            'wechat_article_data_ticket',
            'wechat_article_rand_info',
            'wechat_article_bizuin',
            'wechat_article_slave_sid',
            'wechat_article_slave_user'
          ]
        }
      }
    });

    const configMap: Record<string, string> = {};
    configs.forEach(config => {
      configMap[config.name] = config.value;
    });

    const token = configMap.wechat_article_token;
    const dataTicket = configMap.wechat_article_data_ticket;

    // 动态构建Cookie字符串
    const cookieFields = [
      { key: 'data_ticket', name: 'wechat_article_data_ticket' },
      { key: 'rand_info', name: 'wechat_article_rand_info' },
      { key: 'bizuin', name: 'wechat_article_bizuin' },
      { key: 'slave_sid', name: 'wechat_article_slave_sid' },
      { key: 'slave_user', name: 'wechat_article_slave_user' }
    ];

    const cookiePairs = cookieFields
      .filter(field => configMap[field.name])
      .map(field => `${field.key}=${configMap[field.name]}`);

    const cookie = cookiePairs.join(';');

    if (!token || !cookie || !dataTicket) {
      console.log('⚠️ 微信登录凭证不完整:', {
        hasToken: !!token,
        hasCookie: !!cookie,
        hasDataTicket: !!dataTicket,
        cookieFieldsCount: cookiePairs.length
      });
      return null;
    }

    return { token, cookie, dataTicket };
  } catch (error) {
    console.error('❌ 获取微信登录凭证失败:', error);
    return null;
  }
}

// 调用微信文章发布接口
async function fetchWechatArticles(
  fakeid: string,
  credentials: { token: string; cookie: string; dataTicket: string }
): Promise<any[] | null> {
  const apiUrl = 'https://mp.weixin.qq.com/cgi-bin/appmsgpublish';
  const params = new URLSearchParams({
    sub: 'list',
    search_field: '1',
    begin: '0',
    count: '5',
    query: '',
    fakeid: fakeid,
    type: '101_1',
    free_publish_type: '1',
    sub_action: 'list_ex',
    token: credentials.token,
    lang: 'zh_CN',
    f: 'json',
    ajax: '1'
  });

  const headers = {
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Cache-Control': 'no-cache',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'Cookie': credentials.cookie,
    'Origin': 'https://mp.weixin.qq.com',
    'Pragma': 'no-cache',
    'Referer': 'https://mp.weixin.qq.com/cgi-bin/appmsgpublish?sub=list&search_field=null&begin=0&count=5&query=&fakeid=' + fakeid + '&type=101_1&free_publish_type=1&sub_action=list_ex&token=' + credentials.token + '&lang=zh_CN&f=json&ajax=1',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Requested-With': 'XMLHttpRequest'
  };

  try {
    console.log(`📡 调用微信API: ${apiUrl}`);
    console.log(`🔑 使用token: ${credentials.token.substring(0, 10)}...`);

    const response = await fetch(`${apiUrl}?${params.toString()}`, {
      method: 'GET',
      headers: headers
    });

    if (!response.ok) {
      console.error(`❌ 微信API调用失败: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    console.log(`📊 微信API响应状态: ${data.base_resp?.ret || 'unknown'}`);

    if (data.base_resp?.ret !== 0) {
      console.error(`❌ 微信API返回错误: ${data.base_resp?.err_msg || '未知错误'}`);
      return null;
    }

    const articles = data.publish_page?.publish_list || [];
    console.log(`📄 获取到 ${articles.length} 篇文章`);

    // 处理文章数据
    return articles.map((item: any) => ({
      title: item.title,
      link: item.link,
      digest: item.digest,
      cover: item.cover,
      create_time: item.create_time,
      isMainArticle: item.is_top === 1 // 第一篇为主文章
    }));

  } catch (error) {
    console.error('❌ 调用微信API异常:', error);
    return null;
  }
}

// 保存新文章的辅助方法
async function saveNewArticles(accountId: string, articles: any[]): Promise<number> {
  let savedCount = 0;
  const savedMainArticles: any[] = [];
  const savedSubArticles: any[] = [];

  for (const article of articles) {
    try {
      const title = article.title || '无标题';
      const url = article.link || '';
      const summary = article.digest || '';
      const cover = article.cover || '';
      const isMainArticle = article.isMainArticle || false;

      const publishTime = article.create_time
        ? new Date(article.create_time * 1000)
        : new Date();

      // 检查文章是否已存在
      const existingArticle = await prisma.article.findFirst({
        where: {
          wechatAccountId: accountId,
          url: url
        }
      });

      if (existingArticle) {
        console.log(`⏭️ 文章已存在，跳过: ${title}`);
        continue;
      }

      // 保存新文章
      const newArticle = await prisma.article.create({
        data: {
          title: title,
          url: url,
          summary: summary,
          coverImage: cover || null,
          publishDate: publishTime,
          wechatAccountId: accountId,
          author: article.author || '',
          tags: ''
        }
      });

      console.log(`✅ ${isMainArticle ? '主' : '副'}文章保存成功: ${title}`);
      savedCount++;

      if (isMainArticle) {
        savedMainArticles.push(newArticle);
      } else {
        savedSubArticles.push(newArticle);
      }
    } catch (error) {
      console.error('❌ 保存文章失败:', error);
    }
  }

  // 发送webhook通知
  try {
    // 主文章逐个发送通知
    for (const mainArticle of savedMainArticles) {
      await sendWebhookNotifications(mainArticle, accountId, true);
    }

    // 副文章合并发送通知
    if (savedSubArticles.length > 0) {
      await sendSubArticlesNotification(savedSubArticles, accountId);
    }
  } catch (error) {
    console.error('❌ 发送webhook通知失败:', error);
  }

  return savedCount;
}

// 发送webhook通知的辅助方法
async function sendWebhookNotifications(article: any, accountId: string, isMainArticle: boolean = false): Promise<void> {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: {
        wechatAccountId: accountId,
        isActive: true,
        user: {
          webhookEnabled: true,
          webhookUrl: { not: null },
          webhookType: { not: null }
        }
      },
      include: {
        user: {
          select: {
            webhookUrl: true,
            webhookType: true
          }
        },
        wechatAccount: {
          select: { name: true }
        }
      }
    });

    if (subscriptions.length === 0) return;

    const articleInfo = {
      title: article.title,
      url: article.url,
      summary: article.summary,
      publishDate: article.publishDate.toISOString(),
      accountName: subscriptions[0].wechatAccount.name,
      coverImage: article.coverImage,
      isMainArticle: isMainArticle
    };

    for (const subscription of subscriptions) {
      try {
        await WebhookNotificationService.sendNewArticleNotification(
          subscription.user.webhookUrl!,
          subscription.user.webhookType!,
          articleInfo
        );
      } catch (error) {
        console.error('❌ 发送webhook通知失败:', error);
      }
    }
  } catch (error) {
    console.error('❌ 获取订阅信息失败:', error);
  }
}

// 发送副文章合并通知的辅助方法
async function sendSubArticlesNotification(subArticles: any[], accountId: string): Promise<void> {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: {
        wechatAccountId: accountId,
        isActive: true,
        user: {
          webhookEnabled: true,
          webhookUrl: { not: null },
          webhookType: { not: null }
        }
      },
      include: {
        user: {
          select: {
            webhookUrl: true,
            webhookType: true
          }
        },
        wechatAccount: {
          select: { name: true }
        }
      }
    });

    if (subscriptions.length === 0) return;

    const subArticlesInfo = {
      articles: subArticles.map(article => ({
        title: article.title,
        url: article.url,
        summary: article.summary
      })),
      accountName: subscriptions[0].wechatAccount.name,
      publishDate: subArticles[0].publishDate.toISOString()
    };

    for (const subscription of subscriptions) {
      try {
        await WebhookNotificationService.sendSubArticlesNotification(
          subscription.user.webhookUrl!,
          subscription.user.webhookType!,
          subArticlesInfo
        );
      } catch (error) {
        console.error('❌ 发送副文章合并通知失败:', error);
      }
    }
  } catch (error) {
    console.error('❌ 获取订阅信息失败:', error);
  }
}
