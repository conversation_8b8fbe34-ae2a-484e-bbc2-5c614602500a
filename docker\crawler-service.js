#!/usr/bin/env node

/**
 * FeedWe 定时任务爬虫服务
 * 专门用于Docker部署的独立定时任务服务
 */

const http = require('http');
const { PrismaClient } = require('@prisma/client');

// 动态导入 ES 模块
let WechatCrawlerScheduler;
let initializeApp;
let cleanupApp;

async function loadModules() {
  try {
    // 使用动态导入加载 TypeScript 模块
    const schedulerModule = await import('./lib/wechat-crawler-scheduler.ts');
    const startupModule = await import('./lib/startup.ts');

    WechatCrawlerScheduler = schedulerModule.WechatCrawlerScheduler;
    initializeApp = startupModule.initializeApp;
    cleanupApp = startupModule.cleanupApp;

    console.log('✅ 模块加载成功');
  } catch (error) {
    console.error('❌ 模块加载失败:', error);
    console.error('尝试使用 tsx 运行 TypeScript 文件...');
    process.exit(1);
  }
}

// 创建健康检查服务器
function createHealthServer() {
  const server = http.createServer((req, res) => {
    if (req.url === '/health') {
      const scheduler = WechatCrawlerScheduler?.getInstance();
      const status = scheduler?.getStatus() || { isRunning: false, isActive: false };
      
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'ok',
        timestamp: new Date().toISOString(),
        scheduler: status,
        uptime: process.uptime()
      }));
    } else if (req.url === '/status') {
      const scheduler = WechatCrawlerScheduler?.getInstance();
      const status = scheduler?.getStatus() || { isRunning: false, isActive: false };
      
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        service: 'FeedWe Crawler Service',
        version: '1.0.0',
        status: status,
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      }));
    } else {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Not Found');
    }
  });

  const port = process.env.PORT || 3001;
  server.listen(port, () => {
    console.log(`🏥 健康检查服务器启动在端口 ${port}`);
    console.log(`   健康检查: http://localhost:${port}/health`);
    console.log(`   状态查询: http://localhost:${port}/status`);
  });

  return server;
}

// 主函数
async function main() {
  console.log('🚀 启动 FeedWe 定时任务爬虫服务...');
  console.log(`📅 启动时间: ${new Date().toISOString()}`);
  console.log(`🌍 时区: ${process.env.TZ || 'UTC'}`);
  console.log(`🔧 环境: ${process.env.NODE_ENV || 'development'}`);

  try {
    // 加载模块
    await loadModules();

    // 测试数据库连接
    const prisma = new PrismaClient();
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    await prisma.$disconnect();

    // 创建健康检查服务器
    const healthServer = createHealthServer();

    // 初始化应用（启动定时任务）
    initializeApp();

    // 优雅关闭处理
    const gracefulShutdown = (signal) => {
      console.log(`\n🛑 收到 ${signal} 信号，开始优雅关闭...`);
      
      // 关闭健康检查服务器
      healthServer.close(() => {
        console.log('🏥 健康检查服务器已关闭');
      });

      // 清理应用资源
      cleanupApp();

      // 退出进程
      setTimeout(() => {
        console.log('✅ 服务已完全关闭');
        process.exit(0);
      }, 2000);
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    console.log('✅ FeedWe 定时任务爬虫服务启动完成');
    console.log('🔄 定时任务正在运行中...');

  } catch (error) {
    console.error('❌ 服务启动失败:', error);
    process.exit(1);
  }
}

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

// 启动服务
main().catch((error) => {
  console.error('❌ 主函数执行失败:', error);
  process.exit(1);
});
