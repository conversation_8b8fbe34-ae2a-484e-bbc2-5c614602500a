# GitHub Actions 自动构建 Docker 镜像指南

## 概述

我们创建了两个 GitHub Actions 工作流来自动构建和发布 Docker 镜像到阿里云镜像仓库：

1. **docker-build.yml** - 主分支推送时自动构建
2. **docker-release.yml** - 发布版本时构建稳定版镜像

## 🔧 设置步骤

### 1. 配置 GitHub Secrets

在 GitHub 仓库的 `Settings > Secrets and variables > Actions` 中添加以下 Secrets：

| Secret 名称 | 说明 | 示例值 |
|------------|------|--------|
| `ALIYUN_REGISTRY_USERNAME` | 阿里云镜像仓库用户名 | `your_username` |
| `ALIYUN_REGISTRY_PASSWORD` | 阿里云镜像仓库密码 | `your_password` |

### 2. 阿里云镜像仓库配置

默认配置：
- **地域**: 杭州 (`cn-hangzhou`)
- **命名空间**: `feedwe`
- **镜像名称**: `crawler`
- **完整地址**: `registry.cn-hangzhou.aliyuncs.com/feedwe/crawler`

如需修改，请编辑工作流文件中的 `env` 部分。

### 3. 使用设置脚本（推荐）

运行自动设置脚本：

```bash
chmod +x scripts/setup-aliyun-registry.sh
./scripts/setup-aliyun-registry.sh
```

脚本会自动：
- 测试阿里云登录
- 更新配置文件
- 创建本地构建脚本
- 生成 GitHub Secrets 设置说明

## 🚀 工作流说明

### docker-build.yml（开发构建）

**触发条件**:
- 推送到 `main` 或 `master` 分支
- 修改相关文件（Dockerfile、docker目录、lib目录等）
- 手动触发

**生成的镜像标签**:
- `latest` - 最新版本
- `main-{sha}` - 带提交哈希的版本
- `{branch}` - 分支名版本

**特性**:
- 多平台构建 (linux/amd64, linux/arm64)
- 构建缓存优化
- 自动更新 docker-compose 文件

### docker-release.yml（发布构建）

**触发条件**:
- 创建 GitHub Release
- 手动触发（需指定版本号）

**生成的镜像标签**:
- `{version}` - 指定版本号
- `stable` - 稳定版本
- `latest` - 最新版本

**特性**:
- 生成发布说明
- 多平台构建
- 版本化管理

## 📋 使用方法

### 自动构建

1. **开发版本**：推送代码到主分支即可触发构建
   ```bash
   git push origin main
   ```

2. **发布版本**：创建 GitHub Release
   ```bash
   # 创建标签
   git tag v1.0.0
   git push origin v1.0.0
   
   # 在 GitHub 上创建 Release
   ```

### 手动构建

1. **开发版本**：
   - 进入 GitHub Actions 页面
   - 选择 "Build and Push Docker Image"
   - 点击 "Run workflow"

2. **发布版本**：
   - 进入 GitHub Actions 页面
   - 选择 "Release Docker Image"
   - 输入版本号（如 v1.0.0）
   - 点击 "Run workflow"

### 本地构建和推送

使用生成的脚本：

```bash
# 构建并推送到阿里云
./scripts/build-and-push.sh [tag]

# 示例
./scripts/build-and-push.sh v1.0.0
./scripts/build-and-push.sh latest
```

## 🔄 部署使用

### 1. 使用预构建镜像（推荐）

```yaml
# docker-compose.crawler.yml
services:
  feedwe-crawler:
    image: registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:latest
```

### 2. 从镜像仓库部署

```bash
# 使用部署脚本
./scripts/deploy-from-registry.sh [tag]

# 示例
./scripts/deploy-from-registry.sh latest
./scripts/deploy-from-registry.sh v1.0.0
```

### 3. 手动部署

```bash
# 拉取镜像
docker pull registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:latest

# 更新 docker-compose 文件中的镜像标签
# 然后重新部署
docker-compose -f docker-compose.crawler.yml up -d
```

## 🔍 监控和调试

### 查看构建状态

1. 进入 GitHub 仓库的 Actions 页面
2. 查看最近的工作流运行状态
3. 点击具体的运行查看详细日志

### 常见问题

1. **登录失败**
   - 检查 GitHub Secrets 中的用户名和密码
   - 确认阿里云镜像仓库访问权限

2. **构建失败**
   - 查看 Actions 日志中的错误信息
   - 检查 Dockerfile.crawler 语法

3. **推送失败**
   - 确认镜像仓库地址正确
   - 检查网络连接

### 本地测试

```bash
# 测试 Docker 构建
docker build -f Dockerfile.crawler -t test-image .

# 测试登录阿里云
docker login registry.cn-hangzhou.aliyuncs.com

# 测试推送
docker tag test-image registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:test
docker push registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:test
```

## 🛠️ 自定义配置

### 修改镜像仓库配置

编辑 `.github/workflows/docker-build.yml` 和 `.github/workflows/docker-release.yml` 中的 `env` 部分：

```yaml
env:
  REGISTRY: registry.cn-hangzhou.aliyuncs.com  # 修改地域
  NAMESPACE: your-namespace                     # 修改命名空间
  IMAGE_NAME: your-image-name                   # 修改镜像名
```

### 添加构建参数

在工作流中添加构建参数：

```yaml
- name: Build and push Docker image
  uses: docker/build-push-action@v5
  with:
    context: .
    file: ./Dockerfile.crawler
    push: true
    tags: ${{ steps.meta.outputs.tags }}
    build-args: |
      NODE_ENV=production
      BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
```

### 添加通知

可以添加构建成功/失败的通知：

```yaml
- name: Notify on success
  if: success()
  run: |
    echo "✅ Docker 镜像构建成功"
    # 可以添加钉钉、企业微信等通知

- name: Notify on failure
  if: failure()
  run: |
    echo "❌ Docker 镜像构建失败"
    # 可以添加失败通知
```

## 📚 相关文档

- [GitHub Actions 文档](https://docs.github.com/en/actions)
- [Docker Build Push Action](https://github.com/docker/build-push-action)
- [阿里云容器镜像服务](https://help.aliyun.com/product/60716.html)

## 🆘 技术支持

如果遇到问题：

1. 查看 GitHub Actions 运行日志
2. 检查阿里云镜像仓库配置
3. 验证 GitHub Secrets 设置
4. 参考本文档的故障排除部分
