# FeedWe Docker 部署总结

## 🎯 部署架构

由于 Vercel 免费版本不支持定时任务，我们将系统分为两部分：

```
┌─────────────────┐    ┌─────────────────┐
│   Vercel 部署   │    │  Docker 服务    │
│                 │    │                 │
│ • Web 界面      │    │ • 定时爬取任务  │
│ • API 服务      │    │ • Webhook 通知  │
│ • 用户管理      │    │ • 健康检查      │
│ • 手动爬取      │    │                 │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
                共享数据库
```

## 📁 新增文件说明

### Docker 配置文件
- `Dockerfile.crawler` - 定时任务服务的 Docker 镜像配置
- `docker-compose.crawler.yml` - Docker Compose 配置文件
- `.env.crawler.example` - Docker 环境变量模板

### 服务脚本
- `docker/crawler-service.js` - 定时任务服务主程序
- `docker/start.sh` - 服务启动脚本
- `docker/deploy.sh` - 一键部署脚本
- `docker/manage.sh` - 服务管理脚本

### GitHub Actions
- `.github/workflows/docker-build.yml` - 自动构建工作流
- `.github/workflows/docker-release.yml` - 发布版本工作流
- `scripts/setup-aliyun-registry.sh` - 阿里云镜像仓库设置脚本
- `scripts/build-and-push.sh` - 本地构建推送脚本（自动生成）
- `scripts/deploy-from-registry.sh` - 从镜像仓库部署脚本（自动生成）

### 配置文件
- `.env.vercel.example` - Vercel 环境变量模板
- `vercel.json` - 已更新，移除定时任务配置

### 文档
- `DEPLOYMENT.md` - 完整部署指南
- `docs/docker-deployment.md` - Docker 部署详细说明
- `docker/README.md` - Docker 服务说明

## 🚀 快速部署步骤

### 1. Vercel 部署（Web 应用）

在 Vercel Dashboard 设置环境变量：
```env
DATABASE_URL="mysql://username:password@host:port/database_name"
JWT_SECRET="your_jwt_secret_here"
DISABLE_CRAWLER_SCHEDULER=true  # 必须设置为 true
WECHAT_ARTICLE_COOKIE_STRING="your_wechat_cookie_string_here"
WECHAT_ARTICLE_TOKEN="your_wechat_token_here"
NODE_ENV=production
```

然后部署：
```bash
vercel --prod
```

### 2. Docker 部署（定时任务服务）

#### 方式一：使用预构建镜像（推荐）

1. **设置 GitHub Actions 自动构建**：
   ```bash
   # 运行设置脚本
   chmod +x scripts/setup-aliyun-registry.sh
   ./scripts/setup-aliyun-registry.sh
   ```

2. **配置环境变量**：
   ```bash
   cp .env.crawler.example .env.crawler
   # 编辑 .env.crawler 文件
   ```

3. **从镜像仓库部署**：
   ```bash
   ./scripts/deploy-from-registry.sh latest
   ```

#### 方式二：本地构建部署

```bash
# 配置环境变量
cp .env.crawler.example .env.crawler

# 本地构建部署
./docker/deploy.sh
```

## 🔧 服务管理

使用管理脚本：
```bash
# Linux/Mac
chmod +x docker/manage.sh
./docker/manage.sh [命令]

# Windows
docker/manage.sh [命令]
```

可用命令：
- `start` - 启动服务
- `stop` - 停止服务
- `restart` - 重启服务
- `status` - 查看状态
- `logs` - 查看日志
- `health` - 健康检查
- `deploy` - 部署/更新
- `clean` - 清理服务
- `backup` - 备份配置

## 🔍 监控端点

- **健康检查**: `http://localhost:3001/health`
- **状态查询**: `http://localhost:3001/status`

## ⚙️ 关键配置

### Vercel 环境变量
- `DISABLE_CRAWLER_SCHEDULER=true` - 禁用定时任务
- 其他配置与原来相同

### Docker 环境变量
- `DISABLE_CRAWLER_SCHEDULER=false` - 启用定时任务
- `DATABASE_URL` - 与 Vercel 使用相同数据库
- `WECHAT_ARTICLE_COOKIE_STRING` - 微信登录凭据
- `WECHAT_ARTICLE_TOKEN` - 微信访问令牌
- `WEWORK_WEBHOOK_URL` - 企业微信通知地址（可选）

## 🛠️ 代码修改说明

### 1. 移除 Vercel 定时任务
- 更新 `vercel.json`，移除 `crons` 配置
- 添加环境变量 `DISABLE_CRAWLER_SCHEDULER=true`

### 2. 增强自动初始化
- 更新 `lib/auto-init.ts`，改进 Vercel 环境检测
- 确保在 Vercel 环境中自动禁用定时任务

### 3. 创建独立服务
- `docker/crawler-service.js` - 专门的定时任务服务
- 包含健康检查、优雅关闭等功能

### 4. 修复 Docker 构建问题
- 修复文件路径和模块导入问题
- 添加 TypeScript 运行时支持（tsx）
- 优化 Dockerfile 构建流程

## 🔒 安全建议

1. **环境变量安全**
   - 不要将 `.env.crawler` 提交到版本控制
   - 定期更换敏感凭据

2. **网络安全**
   - 使用防火墙限制端口访问
   - 配置 HTTPS 反向代理

3. **监控告警**
   - 设置服务异常告警
   - 定期检查日志

## 💰 成本优化

- **Vercel**: 免费版本足够使用
- **Docker 服务**: 1核1G内存的 VPS 即可
- **数据库**: 可使用免费的云数据库服务

## 📞 技术支持

如遇问题，请：
1. 查看服务日志
2. 检查环境变量配置
3. 确认网络连接
4. 参考部署文档

详细说明请参考：
- [完整部署指南](DEPLOYMENT.md)
- [Docker 部署详情](docs/docker-deployment.md)
