import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../../lib/auth/admin';
import { prisma } from '../../../../../lib/prisma';


interface WechatAccountSearchResult {
  fakeid: string;
  nickname: string;
  alias: string;
  round_head_img: string;
  service_type: number;
  signature: string;
  verify_status: number;
}

interface SearchResponse {
  base_resp: {
    ret: number;
    err_msg: string;
  };
  list: WechatAccountSearchResult[];
}

// 搜索微信公众号
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const { searchParams } = new URL(request.url);
    const keyword = searchParams.get('keyword');
    const begin = searchParams.get('begin') || '0';
    const size = searchParams.get('size') || '5';

    if (!keyword) {
      return NextResponse.json(
        { error: '缺少搜索关键词' },
        { status: 400 }
      );
    }

    // 从数据库获取微信登录相关配置
    const configNames = [
      'wechat_article_token',
      'wechat_article_cookie_string',
      'wechat_article_data_ticket',
      'wechat_article_rand_info',
      'wechat_article_bizuin',
      'wechat_article_slave_sid',
      'wechat_article_slave_user'
    ];

    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: configNames
        }
      }
    });

    const configMap: Record<string, string> = {};
    configs.forEach(config => {
      configMap[config.name] = config.value;
    });

    // 检查必要的配置
    if (!configMap.wechat_article_token) {
      return NextResponse.json(
        { error: '未找到微信登录token，请先完成微信登录' },
        { status: 401 }
      );
    }

    if (!configMap.wechat_article_cookie_string) {
      return NextResponse.json(
        { error: '未找到微信登录cookie，请先完成微信登录' },
        { status: 401 }
      );
    }

    console.log(`🔍 搜索微信公众号: ${keyword}`);
    console.log('🔑 使用的配置:', {
      hasToken: !!configMap.wechat_article_token,
      hasCookie: !!configMap.wechat_article_cookie_string,
      hasDataTicket: !!configMap.wechat_article_data_ticket
    });

    // 调用微信公众号搜索API
    const searchUrl = `https://exporter.wxdown.online/api/searchbiz`;
    const params = new URLSearchParams({
      keyword: keyword,
      begin: begin,
      size: size,
      token: configMap.wechat_article_token,
      // 添加关键参数
      data_ticket: configMap.wechat_article_data_ticket || '',
      rand_info: configMap.wechat_article_rand_info || '',
      bizuin: configMap.wechat_article_bizuin || '',
      slave_sid: configMap.wechat_article_slave_sid || '',
      slave_user: configMap.wechat_article_slave_user || ''
    });

    const response = await fetch(`${searchUrl}?${params}`, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://mp.weixin.qq.com/',
        'Origin': 'https://mp.weixin.qq.com',
        // 添加完整的Cookie
        'Cookie': configMap.wechat_article_cookie_string
      }
    });

    if (!response.ok) {
      throw new Error(`搜索API请求失败: ${response.status} ${response.statusText}`);
    }

    const result: SearchResponse = await response.json();
    console.log('🔍 搜索结果:', result);

    if (result.base_resp?.ret !== 0) {
      throw new Error(`搜索API返回错误: ${result.base_resp?.err_msg || '未知错误'}`);
    }

    // 检查哪些公众号已经存在于数据库中
    const existingAccounts = await prisma.wechatAccount.findMany({
      where: {
        fakeid: {
          in: result.list.map(item => item.fakeid)
        }
      },
      select: { fakeid: true }
    });

    const existingFakeIds = new Set(existingAccounts.map(acc => acc.fakeid));

    // 格式化搜索结果
    const formattedResults = result.list.map(item => ({
      fakeid: item.fakeid,
      nickname: item.nickname,
      alias: item.alias,
      avatar: item.round_head_img,
      serviceType: item.service_type,
      signature: item.signature,
      verifyStatus: item.verify_status,
      isAdded: existingFakeIds.has(item.fakeid) // 标记是否已添加
    }));

    // 直接返回原始结果，不进行URL转换
    // URL转换应该在前端显示时进行，而不是在API层
    return NextResponse.json({
      success: true,
      results: formattedResults,
      total: formattedResults.length
    });
  } catch (error) {
    console.error('搜索微信公众号失败:', error);
    const errorMessage = error instanceof Error ? error.message : '搜索失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 添加微信公众号到数据库
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const { 
      fakeid, 
      nickname, 
      alias, 
      avatar, 
      serviceType, 
      signature, 
      verifyStatus 
    } = await request.json();

    if (!fakeid || !nickname) {
      return NextResponse.json(
        { error: '缺少必要参数：fakeid 和 nickname' },
        { status: 400 }
      );
    }

    // 检查是否已存在
    const existingAccount = await prisma.wechatAccount.findUnique({
      where: { fakeid: fakeid }
    });

    if (existingAccount) {
      return NextResponse.json(
        { error: '该公众号已存在' },
        { status: 400 }
      );
    }

    console.log('💾 准备保存公众号数据:', {
      nickname,
      avatar,
      signature,
      fakeid
    });

    // 创建新的微信公众号记录
    const newAccount = await prisma.wechatAccount.create({
      data: {
        name: nickname,
        nickname: nickname,
        alias: alias || '',
        avatar: avatar || '',
        roundHeadImg: avatar || '',
        description: signature || '',
        signature: signature || '',
        fakeid: fakeid,
        serviceType: serviceType || 0,
        verifyStatus: verifyStatus || 0
      }
    });

    console.log(`✅ 成功添加微信公众号: ${nickname} (${fakeid})`);
    console.log('💾 保存的头像URL:', newAccount.avatar);

    return NextResponse.json({
      success: true,
      account: {
        id: newAccount.id,
        name: newAccount.name,
        nickname: newAccount.nickname,
        fakeid: newAccount.fakeid,
        avatar: newAccount.avatar,
        signature: newAccount.signature,
        verifyStatus: newAccount.verifyStatus
      },
      message: '公众号添加成功'
    });
  } catch (error) {
    console.error('添加微信公众号失败:', error);
    const errorMessage = error instanceof Error ? error.message : '添加公众号失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
