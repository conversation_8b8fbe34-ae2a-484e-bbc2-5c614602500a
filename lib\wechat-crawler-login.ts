import { EventEmitter } from 'events';

// ==================== 类型定义 ====================

interface LoginSession {
  id: string;
  sessionid?: string;
  qrcode?: string;
  status: 'pending' | 'scanned' | 'confirmed' | 'expired' | 'failed';
  token?: string;
  extractedToken?: string;
  cookies?: string[];
  cookieComplete?: boolean;
  criticalFieldsCount?: number;
  userInfo?: {
    nickname: string;
    avatar: string;
  };
  createdAt: Date;
  expiresAt: Date;
}

interface WechatLoginOptions {
  onStatusChange?: (status: string, data?: any) => void;
  onQRCode?: (qrcode: string) => void;
  onSuccess?: (userInfo: any, token: string) => void;
  onError?: (error: Error) => void;
  onCookieSave?: (cookies: Record<string, string>, token: string) => Promise<void>;
}

// ==================== 主类 ====================

class WechatCrawlerLogin extends EventEmitter {
  private session: LoginSession | null = null;
  private checkInterval: NodeJS.Timeout | null = null;
  private options: WechatLoginOptions;
  private cookies: string[] = [];
  private isProcessingFinalLogin: boolean = false;

  constructor(options: WechatLoginOptions = {}) {
    super();
    this.options = options;
  }

  // ==================== 公共接口 ====================

  /**
   * 开始登录流程
   * @returns 二维码数据URL
   */
  async startLogin(): Promise<string> {
    try {
      console.log('🚀 开始微信公众平台登录流程...');

      // 步骤1: 初始化登录会话
      await this.initializeLogin();

      // 步骤2: 获取二维码
      const qrcode = await this.getQRCode();

      // 步骤3: 开始轮询检查状态
      this.startPolling();

      return qrcode;
    } catch (error) {
      console.error('❌ 登录流程启动失败:', error);
      this.options.onError?.(error as Error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 获取当前登录会话
   */
  getSession(): LoginSession | null {
    return this.session;
  }

  /**
   * 取消登录流程
   */
  cancelLogin(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    if (this.session) {
      this.session.status = 'expired';
    }
    console.log('🚫 登录流程已取消');
  }

  // ==================== 登录流程实现 ====================

  /**
   * 初始化登录会话
   */
  private async initializeLogin(): Promise<void> {
    // 创建新的登录会话
    this.session = {
      id: this.generateSessionId(),
      status: 'pending',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5分钟过期
      cookies: []
    };

    // 启动微信登录并获取初始Cookie
    await this.startWechatLogin();
  }

  /**
   * 启动微信登录并获取Cookie
   */
  private async startWechatLogin(): Promise<void> {
    try {
      console.log('🚀 启动微信登录...');

      // 生成sessionid
      const sessionid = `${Date.now()}${Math.floor(Math.random() * 100)}`;
      this.session!.sessionid = sessionid;

      const url = 'https://mp.weixin.qq.com/cgi-bin/bizlogin?action=startlogin';
      const headers = {
        'Referer': 'https://mp.weixin.qq.com/',
        'Origin': 'https://mp.weixin.qq.com',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
        'Content-Type': 'application/x-www-form-urlencoded',
      };
      const bodyParams = {
        userlang: 'zh_CN',
        redirect_url: '',
        login_type: '3',
        sessionid: sessionid,
      };
      const body = new URLSearchParams(bodyParams);

      console.log('📤 启动登录请求详情:');
      console.log('  URL:', url);
      console.log('  Method: POST');
      console.log('  Headers:', headers);
      console.log('  Body Params:', bodyParams);
      console.log('  Body String:', body.toString());

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body
      });

      console.log('📥 启动登录响应详情:');
      console.log('  Status:', response.status, response.statusText);
      console.log('  Headers:', Object.fromEntries(response.headers.entries()));

      // 保存cookies
      const setCookieHeaders = response.headers.getSetCookie?.() || [];
      this.session!.cookies = setCookieHeaders;
      this.cookies = []; // 初始化为空数组
      this.addCookies(setCookieHeaders); // 使用addCookies方法添加
      console.log('  Set-Cookie Headers:', setCookieHeaders);

      const result = await response.json();
      console.log('  Body:', result);

      if (result.base_resp?.ret !== 0) {
        throw new Error('启动登录失败: ' + (result.base_resp?.err_msg || '未知错误'));
      }
    } catch (error) {
      console.error('❌ 启动微信登录失败:', error);
      throw error;
    }
  }

  // 步骤2: 获取二维码
  private async getQRCode(): Promise<string> {
    try {
      console.log('📱 获取二维码...');

      const random = Date.now();
      const url = `https://mp.weixin.qq.com/cgi-bin/scanloginqrcode?action=getqrcode&random=${random}`;
      const headers = {
        'Referer': 'https://mp.weixin.qq.com/',
        'Origin': 'https://mp.weixin.qq.com',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
        'Cookie': this.session!.cookies?.join('; ') || ''
      };

      console.log('📤 二维码请求详情:');
      console.log('  URL:', url);
      console.log('  Method: GET');
      console.log('  Headers:', headers);
      console.log('  Random:', random);
      console.log('  Cookies:', this.session!.cookies);

      const response = await fetch(url, {
        method: 'GET',
        headers
      });

      console.log('📥 二维码响应详情:');
      console.log('  Status:', response.status, response.statusText);
      console.log('  Headers:', Object.fromEntries(response.headers.entries()));
      console.log('  Content-Type:', response.headers.get('content-type'));
      console.log('  Content-Length:', response.headers.get('content-length'));

      if (!response.ok) {
        throw new Error(`获取二维码失败: ${response.status} ${response.statusText}`);
      }

      const qrBuffer = await response.arrayBuffer();
      const qrBase64 = Buffer.from(qrBuffer).toString('base64');

      console.log('  二维码数据大小:', qrBuffer.byteLength, 'bytes');
      console.log('  Base64长度:', qrBase64.length);
      console.log('📱 二维码获取成功');

      return `data:image/jpeg;base64,${qrBase64}`;
    } catch (error) {
      console.error('❌ 获取二维码失败:', error);
      throw error;
    }
  }

  // 步骤3: 检查登录状态
  private async checkLoginStatus(): Promise<void> {
    if (!this.session) return;

    try {
      console.log(`🔍 检查登录状态 - 会话ID: ${this.session.id}, 当前状态: ${this.session.status}`);

      // 调用真实的微信API检查扫码状态 - 使用GET方法
      const url = 'https://mp.weixin.qq.com/cgi-bin/scanloginqrcode?action=ask&token=&lang=zh_CN&f=json&ajax=1';
      const headers = {
        'Referer': 'https://mp.weixin.qq.com/',
        'Origin': 'https://mp.weixin.qq.com',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
        'Cookie': this.session.cookies?.join('; ') || ''
      };

      console.log('📤 状态检查请求详情:');
      console.log('  URL:', url);
      console.log('  Method: GET');
      console.log('  Headers:', headers);

      const response = await fetch(url, {
        method: 'GET',
        headers
      });

      console.log('📥 状态检查响应详情:');
      console.log('  Status:', response.status, response.statusText);
      console.log('  Headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('  Body:', result);

      console.log('🔄 开始处理状态:', result.status, '类型:', typeof result.status);

      // 根据真实微信API返回的状态处理
      switch (result.status) {
        case 0:
          // 未扫码，继续轮询
          console.log('⏳ 等待扫码...');
          break;
        case 4:
          // 已扫码但未确认，更新状态
          console.log('👀 二维码已扫描，等待确认...');
          if (this.session.status === 'pending') {
            this.session.status = 'scanned';
            this.adjustPollingInterval(); // 调整为更快的轮询频率
            this.options.onStatusChange?.('scanned');
            this.emit('scanned');
          }
          break;
        case 1:
          // 已确认登录，进行最终登录步骤
          console.log('✅ 用户确认登录，开始完成登录流程...');
          console.log('🔍 调试信息 - 准备执行最终登录步骤');
          console.log('  当前时间:', new Date().toISOString());
          console.log('  Session状态:', this.session?.status);
          console.log('  Cookie数量:', this.session?.cookies?.length || 0);
          console.log('  isProcessingFinalLogin:', this.isProcessingFinalLogin);

          // 检查是否已经在处理最终登录
          if (this.isProcessingFinalLogin) {
            console.log('⚠️ 最终登录已在处理中，跳过重复执行');
            return;
          }

          // 立即停止轮询，防止重复执行
          if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
            console.log('⏹️ 已停止状态轮询');
          }

          console.log('🚀 即将调用 performFinalLogin()...');

          try {
            await this.performFinalLogin();
            console.log('✅ performFinalLogin() 执行完成');
          } catch (finalLoginError) {
            console.error('❌ performFinalLogin() 执行失败:', finalLoginError);
            console.error('❌ 错误堆栈:', finalLoginError instanceof Error ? finalLoginError.stack : 'No stack trace');
            // 不重新抛出错误，让错误处理在 performFinalLogin 内部完成
          }
          break;
        default:
          console.log('❓ 未知状态:', result.status, result);
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
      // 不抛出错误，继续轮询
    }
  }

  // 辅助方法
  private generateSessionId(): string {
    return `wx_login_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private formatCookies(): string {
    // 去重Cookie，以最新的值为准，并过滤掉EXPIRED的Cookie
    const cookieMap = new Map<string, string>();

    // 遍历所有Cookie，后面的覆盖前面的
    for (const cookie of this.cookies) {
      const [nameValue] = cookie.split(';'); // 只取name=value部分
      const [name, value] = nameValue.split('=');
      if (name && value !== undefined) {
        const trimmedName = name.trim();
        const trimmedValue = value.trim();

        // 过滤掉值为EXPIRED的Cookie
        if (trimmedValue !== 'EXPIRED') {
          cookieMap.set(trimmedName, `${trimmedName}=${trimmedValue}`);
        } else {
          console.log(`🗑️ 过滤掉EXPIRED Cookie: ${trimmedName}`);
        }
      }
    }

    // 转换为数组并拼接
    const uniqueCookies = Array.from(cookieMap.values());
    console.log(`🍪 Cookie处理: 原始${this.cookies.length}个 -> 去重后${uniqueCookies.length}个`);

    return uniqueCookies.join('; ');
  }

  private addCookies(newCookies: string[]): void {
    if (newCookies.length === 0) return;

    console.log(`🍪 添加新Cookie: ${newCookies.length}个`);

    // 直接添加到数组末尾，formatCookies会处理去重
    this.cookies = [...this.cookies, ...newCookies];

    // 更新session中的cookies
    if (this.session) {
      this.session.cookies = this.cookies;
    }
  }

  private getUniqueCookies(): Record<string, string> {
    // 获取去重后的Cookie对象，用于保存到数据库，过滤掉EXPIRED的Cookie
    const cookieMap = new Map<string, string>();

    // 遍历所有Cookie，后面的覆盖前面的
    for (const cookie of this.cookies) {
      const [nameValue] = cookie.split(';'); // 只取name=value部分
      const [name, value] = nameValue.split('=');
      if (name && value !== undefined) {
        const trimmedName = name.trim();
        const trimmedValue = value.trim();

        // 过滤掉值为EXPIRED的Cookie
        if (trimmedValue !== 'EXPIRED') {
          cookieMap.set(trimmedName, trimmedValue);
        }
      }
    }

    // 转换为对象
    const cookieObject: Record<string, string> = {};
    cookieMap.forEach((value, name) => {
      cookieObject[name] = value;
    });

    return cookieObject;
  }



  private startPolling(): void {
    // 初始使用较快的轮询频率
    this.checkInterval = setInterval(() => {
      this.checkLoginStatus();
    }, 1000); // 每1秒检查一次，提高响应速度
  }

  // 动态调整轮询频率
  private adjustPollingInterval(): void {
    if (!this.session) return;

    // 清除当前轮询
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    let interval = 1000; // 默认1秒

    // 根据状态调整轮询频率
    switch (this.session.status) {
      case 'pending':
        interval = 2000; // 等待扫码时2秒一次
        break;
      case 'scanned':
        interval = 500; // 已扫码等待确认时0.5秒一次，最快响应
        break;
      case 'confirmed':
        interval = 200; // 确认后立即处理，0.2秒一次
        break;
      default:
        interval = 2000;
    }

    console.log(`🔄 调整轮询间隔为 ${interval}ms (状态: ${this.session.status})`);

    this.checkInterval = setInterval(() => {
      this.checkLoginStatus();
    }, interval);
  }



  private async performFinalLogin(): Promise<void> {
    try {
      // 设置标志位，防止重复执行
      this.isProcessingFinalLogin = true;
      console.log('🔄 执行最终登录步骤...');
      console.log('🔍 performFinalLogin 调试信息:');
      console.log('  执行时间:', new Date().toISOString());
      console.log('  Session ID:', this.session?.id);
      console.log('  Session状态:', this.session?.status);
      console.log('  Cookie数量:', this.cookies.length);
      console.log('  Cookie预览:', this.formatCookies().substring(0, 100) + '...');

      // 调用 /cgi-bin/bizlogin?action=login 接口获取token
      const loginUrl = 'https://mp.weixin.qq.com/cgi-bin/bizlogin?action=login';

      // 构建POST请求的body参数
      const bodyParams = {
        userlang: 'zh_CN',
        redirect_url: '',
        cookie_forbidden: 0,
        cookie_cleaned: 0,
        plugin_used: 0,
        login_type: 3,
        token: '',
        lang: 'zh_CN',
        f: 'json',
        ajax: 1,
      };

      const bodyString = new URLSearchParams(bodyParams as any).toString();

      console.log('📤 最终登录请求详情:');
      console.log('  URL:', loginUrl);
      console.log('  Method: POST');
      console.log('  Body Params:', bodyParams);
      console.log('  Body String:', bodyString);
      console.log('  Cookie:', this.formatCookies());

      console.log('🚀 开始发送最终登录请求...');

      // 创建带超时的 fetch 请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.log('⏰ 最终登录请求超时，中止请求');
        controller.abort();
      }, 30000); // 30秒超时

      let loginResponse: Response;

      try {
        loginResponse = await fetch(loginUrl, {
          method: 'POST',
          headers: {
            'Referer': 'https://mp.weixin.qq.com/',
            'Origin': 'https://mp.weixin.qq.com',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': this.formatCookies()
          },
          body: bodyString,
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        console.log('✅ 最终登录请求发送成功，收到响应');

      } catch (fetchError) {
        clearTimeout(timeoutId);
        console.error('❌ 最终登录请求失败:', fetchError);

        if (fetchError instanceof Error) {
          if (fetchError.name === 'AbortError') {
            console.error('  原因: 请求超时 (30秒)');
          } else if (fetchError.message.includes('fetch')) {
            console.error('  原因: 网络连接问题');
          } else {
            console.error('  原因:', fetchError.message);
          }
        }

        throw new Error(`最终登录请求失败: ${fetchError}`);
      }

      console.log('📥 最终登录响应详情:');
      console.log('  Status:', loginResponse.status, loginResponse.statusText);
      console.log('  Headers:', Object.fromEntries(loginResponse.headers.entries()));

      // 检查是否有新的Cookie - 这是最重要的Cookie获取点
      const finalCookies = loginResponse.headers.getSetCookie?.() || [];
      if (finalCookies.length > 0) {
        console.log('🍪 从最终登录获取到新Cookie:', finalCookies.length);
        console.log('🍪 最终登录Cookie详情:', finalCookies);
        this.addCookies(finalCookies);

        // 检查是否包含关键Cookie字段
        const cookieString = finalCookies.join('; ');
        const hasDataTicket = cookieString.includes('data_ticket');
        const hasBizuin = cookieString.includes('bizuin');
        const hasRandInfo = cookieString.includes('rand_info');
        const hasSlaveSid = cookieString.includes('slave_sid');
        const hasSlaveUser = cookieString.includes('slave_user');

        console.log('🔍 关键Cookie字段检查:');
        console.log('  data_ticket:', hasDataTicket ? '✅' : '❌');
        console.log('  bizuin:', hasBizuin ? '✅' : '❌');
        console.log('  rand_info:', hasRandInfo ? '✅' : '❌');
        console.log('  slave_sid:', hasSlaveSid ? '✅' : '❌');
        console.log('  slave_user:', hasSlaveUser ? '✅' : '❌');

        const criticalFieldsCount = [hasDataTicket, hasBizuin, hasRandInfo, hasSlaveSid, hasSlaveUser].filter(Boolean).length;
        console.log(`🎯 关键字段完整度: ${criticalFieldsCount}/5`);

        // 设置Cookie完整度标志和计数
        this.session!.cookieComplete = criticalFieldsCount >= 3;
        this.session!.criticalFieldsCount = criticalFieldsCount;
      } else {
        console.log('⚠️ 最终登录请求未返回新Cookie，这可能是问题所在');
      }

      if (loginResponse.status === 200) {
        const responseText = await loginResponse.text();
        console.log('📄 最终登录响应内容:', responseText);

        try {
          // action=login接口正常情况下只会返回JSON
          const responseJson = JSON.parse(responseText);
          console.log('📊 最终登录JSON响应:', JSON.stringify(responseJson, null, 2));

          // 检查响应是否成功
          if (responseJson.base_resp?.ret === 0) {
            // 从响应中提取token
            if (responseJson.redirect_url) {
              const tokenMatch = responseJson.redirect_url.match(/token=(\d+)/);
              if (tokenMatch && tokenMatch[1]) {
                const token = tokenMatch[1];
                console.log('🔑 从redirect_url提取到token:', token);
                this.session!.extractedToken = token;

                // 保存完整的token到数据库
                await this.saveTokenToDatabase(token);
              } else {
                console.log('⚠️ redirect_url中未找到token');
              }
            } else {
              console.log('⚠️ 响应中没有redirect_url');
            }
          } else {
            console.log('❌ 登录响应失败:', responseJson.base_resp?.err_msg || '未知错误');
            throw new Error(`登录失败: ${responseJson.base_resp?.err_msg || '未知错误'}`);
          }
        } catch (parseError) {
          // action=login接口返回非JSON说明有问题，应该报错
          console.error('❌ action=login接口返回非JSON响应，登录失败');
          console.error('响应内容:', responseText);
          throw new Error('登录接口返回异常响应，登录失败');
        }
      } else {
        console.log('❌ 最终登录请求失败，状态码:', loginResponse.status);
        throw new Error(`登录请求失败，状态码: ${loginResponse.status}`);
      }

      // 如果关键Cookie字段足够，直接完成登录
      if (this.session!.criticalFieldsCount! >= 3) {
        console.log('🚀 关键Cookie字段已足够，直接完成登录流程');
        await this.finishLoginDirectly();
      } else {
        console.log('⚠️ Cookie不完整，继续完整登录流程');
        await this.completeLogin();
      }

    } catch (error) {
      console.error('❌ 最终登录步骤失败:', error);
      console.error('❌ 错误详情:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        session: this.session ? {
          id: this.session.id,
          status: this.session.status,
          cookieCount: this.session.cookies?.length || 0
        } : null
      });

      // 不要重新抛出错误，而是尝试恢复
      console.log('🔄 尝试从错误中恢复...');

      // 如果有session且有cookies，尝试直接完成登录
      if (this.session && this.session.cookies && this.session.cookies.length > 0) {
        console.log('🔄 尝试使用现有Cookie完成登录...');
        try {
          await this.finishLoginDirectly();
          return; // 成功恢复，直接返回
        } catch (recoveryError) {
          console.error('❌ 恢复失败:', recoveryError);
        }
      }

      // 如果恢复失败，触发错误回调
      this.options.onError?.(error instanceof Error ? error : new Error(String(error)));
      this.emit('error', error);

    } finally {
      // 清除标志位
      this.isProcessingFinalLogin = false;
    }
  }

  private async completeLogin(): Promise<void> {
    console.log('✅ 完成登录流程...');
    this.session!.status = 'confirmed';
    this.adjustPollingInterval(); // 调整为最快的处理频率

    try {
      // 获取真实用户信息和保存Cookie
      await this.getRealUserInfoAndSaveCookies();
    } catch (error) {
      console.error('完成登录失败:', error);
      throw error;
    } finally {
      // 停止轮询
      if (this.checkInterval) {
        clearInterval(this.checkInterval);
        this.checkInterval = null;
      }
    }
  }

  private async getRealUserInfoAndSaveCookies(): Promise<void> {
    console.log('🔄 获取真实用户信息并保存Cookie...');

    try {
      // Cookie已经在最终登录请求中获取到了，直接使用
      console.log('🍪 使用最终登录请求中获取的Cookie...');
      console.log('🍪 当前Cookie数量:', this.cookies.length);
      console.log('🍪 当前Cookie预览:', this.formatCookies().substring(0, 100) + '...');

      // 获取用户信息（主要用于验证Cookie有效性）
      const userInfoResponse = await fetch('https://mp.weixin.qq.com/cgi-bin/home?t=home/index&lang=zh_CN', {
        method: 'GET',
        headers: {
          'Referer': 'https://mp.weixin.qq.com/',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
          'Cookie': this.formatCookies()
        }
      });

      console.log('📥 用户信息响应状态:', userInfoResponse.status);

      // 检查是否有额外的Cookie返回（通常不应该有，因为主要Cookie已在登录请求中获取）
      const additionalCookies = userInfoResponse.headers.getSetCookie?.() || [];
      if (additionalCookies.length > 0) {
        console.log('🍪 从用户信息请求获取到额外Cookie:', additionalCookies.length);
        console.log('🍪 额外Cookie详情:', additionalCookies);
        this.addCookies(additionalCookies);
      } else {
        console.log('✅ 用户信息请求未返回额外Cookie，符合预期（主要Cookie已在登录请求中获取）');
      }

      // 从HTML中提取真实的用户信息
      const userInfo = await this.extractUserInfoFromHTML(userInfoResponse);

      // 保存最终的Cookie到数据库
      await this.saveCookiesToDatabase();

      // 生成token
      const token = `wx_real_${Date.now()}`;

      this.session!.userInfo = userInfo;
      this.session!.token = token;

      console.log('✅ 微信登录成功:', userInfo);

      // 触发成功回调
      this.options.onSuccess?.(userInfo, token);
      this.emit('success', userInfo, token);

    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  private async extractUserInfoFromHTML(response: Response): Promise<{ nickname: string; avatar: string }> {
    try {
      console.log('🔍 从HTML中提取用户信息...');

      if (response.status !== 200) {
        console.log('⚠️ 用户信息响应状态异常:', response.status);
        return this.getDefaultUserInfo();
      }

      const html = await response.text();
      console.log('📄 HTML内容长度:', html.length);

      // 提取昵称
      let nickname = '';
      const nicknameMatchResult = html.match(/wx\.cgiData\.nick_name\s*?=\s*?"([^"]+)"/);
      if (nicknameMatchResult && nicknameMatchResult[1]) {
        nickname = nicknameMatchResult[1];
        console.log('✅ 提取到昵称:', nickname);
      } else {
        console.log('⚠️ 未能从HTML中提取到昵称');
        // 尝试其他可能的匹配模式
        const altNicknameMatch = html.match(/nick_name["']?\s*[:=]\s*["']([^"']+)["']/);
        if (altNicknameMatch && altNicknameMatch[1]) {
          nickname = altNicknameMatch[1];
          console.log('✅ 使用备用模式提取到昵称:', nickname);
        }
      }

      // 提取头像
      let avatar = '';
      const headImgMatchResult = html.match(/wx\.cgiData\.head_img\s*?=\s*?"([^"]+)"/);
      if (headImgMatchResult && headImgMatchResult[1]) {
        avatar = headImgMatchResult[1];
        console.log('✅ 提取到头像URL:', avatar);
      } else {
        console.log('⚠️ 未能从HTML中提取到头像');
        // 尝试其他可能的匹配模式
        const altAvatarMatch = html.match(/head_img["']?\s*[:=]\s*["']([^"']+)["']/);
        if (altAvatarMatch && altAvatarMatch[1]) {
          avatar = altAvatarMatch[1];
          console.log('✅ 使用备用模式提取到头像:', avatar);
        }
      }

      // 如果提取失败，使用默认值
      if (!nickname || !avatar) {
        console.log('⚠️ 部分用户信息提取失败，使用默认值');
        const defaultInfo = this.getDefaultUserInfo();
        return {
          nickname: nickname || defaultInfo.nickname,
          avatar: avatar || defaultInfo.avatar
        };
      }

      console.log('✅ 用户信息提取成功:', { nickname, avatar });
      return { nickname, avatar };

    } catch (error) {
      console.error('❌ 提取用户信息时出错:', error);
      return this.getDefaultUserInfo();
    }
  }

  private getDefaultUserInfo(): { nickname: string; avatar: string } {
    return {
      nickname: '微信用户',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
    };
  }

  private async testSearchBizAPI(token: string): Promise<void> {
    try {
      console.log('🔍 测试公众号搜索接口...');

      const searchUrl = `https://mp.weixin.qq.com/cgi-bin/searchbiz?action=search_biz&begin=0&count=5&query=小白学算法&token=${token}&lang=zh_CN&f=json&ajax=1`;

      const response = await fetch(searchUrl, {
        method: 'GET',
        headers: {
          'Referer': 'https://mp.weixin.qq.com/',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
          'Cookie': this.formatCookies(),
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      console.log('📊 搜索接口测试结果:');
      console.log('  - 响应状态:', response.status);
      console.log('  - 请求URL:', searchUrl);

      if (response.status === 200) {
        const result = await response.json();
        console.log('  - 响应内容:', JSON.stringify(result, null, 2));

        if (result.base_resp && result.base_resp.ret === 0) {
          console.log('✅ Cookie和token验证成功 - 可以正常调用微信公众号搜索接口');
          console.log('🎯 搜索结果数量:', result.list ? result.list.length : 0);
        } else {
          console.log('❌ 接口调用失败:', result.base_resp?.err_msg || '未知错误');
        }
      } else {
        console.log('❌ 搜索接口调用失败 - HTTP状态码:', response.status);
        const errorText = await response.text();
        console.log('  - 错误响应:', errorText.substring(0, 200));
      }
    } catch (error) {
      console.error('❌ 搜索接口测试出错:', error);
    }
  }

  private async basicValidation(homeText?: string): Promise<void> {
    try {
      console.log('🔍 执行基础Cookie验证...');

      // 如果有主页内容，先分析主页内容
      if (homeText) {
        console.log('📄 分析主页内容...');

        if (homeText.includes('setting') || homeText.includes('账号信息') || homeText.includes('公众号') || homeText.includes('素材管理')) {
          console.log('✅ 主页内容验证成功 - 已登录状态');
          return;
        } else if (homeText.includes('login') || homeText.includes('登录') || homeText.includes('scan')) {
          console.log('❌ 主页内容验证失败 - 仍在登录页面');
        } else {
          console.log('⚠️ 主页内容不确定，继续其他验证...');
        }
      }

      // 尝试访问设置页面
      const response = await fetch('https://mp.weixin.qq.com/cgi-bin/settingpage?t=setting/index&action=index&token=&lang=zh_CN', {
        method: 'GET',
        headers: {
          'Referer': 'https://mp.weixin.qq.com/',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
          'Cookie': this.formatCookies()
        }
      });

      console.log('📊 设置页面验证结果:');
      console.log('  - 响应状态:', response.status);

      if (response.status === 200) {
        const text = await response.text();

        if (text.includes('setting/index') || text.includes('账号信息') || text.includes('公众号设置')) {
          console.log('✅ 设置页面验证成功');
        } else if (text.includes('login') || text.includes('登录')) {
          console.log('❌ 设置页面验证失败 - 需要重新登录');
        } else {
          console.log('⚠️ 设置页面验证结果不确定');
          console.log('📄 响应内容预览:', text.substring(0, 200) + '...');
        }
      } else {
        console.log('❌ 设置页面验证失败 - HTTP状态码:', response.status);
      }
    } catch (error) {
      console.error('❌ 基础验证出错:', error);
    }
  }



  private async saveTokenToDatabase(token: string): Promise<void> {
    try {
      console.log('💾 保存token到数据库:', token);
      console.log('🔍 开始导入Prisma...');

      // 检查环境
      if (typeof window !== 'undefined') {
        console.log('⚠️ 浏览器环境，跳过token保存');
        return;
      }

      const { prisma } = await import('./prisma');
      console.log('✅ Prisma导入成功');

      console.log('🔍 开始执行数据库upsert操作...');
      await prisma.systemConfig.upsert({
        where: { name: 'wechat_article_token' },
        update: {
          value: token,
          updatedAt: new Date()
        },
        create: {
          name: 'wechat_article_token',
          value: token
        }
      });

      console.log('✅ token保存成功');

      // 断开连接
      await prisma.$disconnect();
      console.log('🔌 数据库连接已断开');

    } catch (error) {
      console.error('❌ 保存token失败:', error);
      console.error('❌ 错误详情:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        token: token
      });

      // 不抛出错误，继续执行后续流程
      console.log('⚠️ token保存失败，但继续执行后续流程');
    }
  }

  private async saveCookiesToDatabase(): Promise<void> {
    console.log('💾 保存Cookie到数据库...');

    // 获取去重后的Cookie对象
    const cookieObject = this.getUniqueCookies();

    console.log('🍪 准备保存的Cookie数量:', Object.keys(cookieObject).length);
    console.log('🍪 Cookie预览:', Object.keys(cookieObject).slice(0, 5));
    console.log('🍪 完整Cookie内容:', JSON.stringify(cookieObject, null, 2));

    try {
      // 直接保存到数据库，避免API调用的认证和网络问题
      await this.saveCookiesDirectly(cookieObject);
      console.log('✅ Cookie保存成功');

    } catch (error) {
      console.error('❌ 保存Cookie到数据库失败:', error);
      throw error;
    }
  }

  stopLogin(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    this.session = null;
  }

  // 快速完成登录流程（当Cookie已足够时）
  private async finishLoginDirectly(): Promise<void> {
    try {
      console.log('⚡ 快速完成登录流程...');

      // 直接保存Cookie到数据库
      await this.saveCookiesToDatabase();

      // 生成token（使用提取的token或生成新的）
      const token = this.session!.extractedToken || `wx_fast_${Date.now()}`;

      // 设置基本用户信息（快速模式）
      const userInfo = {
        nickname: '微信用户',
        avatar: 'https://via.placeholder.com/40'
      };

      this.session!.userInfo = userInfo;
      this.session!.token = token;

      console.log('✅ 快速登录完成:', userInfo);

      // 触发成功回调
      this.options.onSuccess?.(userInfo, token);
      this.emit('success', userInfo, token);

    } catch (error) {
      console.error('❌ 快速登录失败:', error);
      // 如果快速登录失败，回退到完整流程
      console.log('🔄 回退到完整登录流程...');
      await this.completeLogin();
    }
  }

  // 直接保存Cookie到数据库的方法
  private async saveCookiesDirectly(cookieObject: Record<string, string>): Promise<void> {
    try {
      console.log('💾 直接保存Cookie到数据库...');

      // 检查是否在服务器环境中
      if (typeof window !== 'undefined') {
        // 在浏览器环境中，通过回调函数通知外部保存
        console.log('🌐 浏览器环境，通过回调保存Cookie...');
        if (this.options.onCookieSave) {
          await this.options.onCookieSave(cookieObject, this.session!.extractedToken || '');
          return;
        } else {
          throw new Error('浏览器环境中未提供Cookie保存回调函数');
        }
      }

      // 服务器环境中直接使用 Prisma
      console.log('🔍 开始导入Prisma（Cookie保存）...');
      const { prisma } = await import('./prisma');
      console.log('✅ Prisma导入成功（Cookie保存）');

      // 构建Cookie字符串
      const cookieString = Object.entries(cookieObject)
        .map(([key, value]) => `${key}=${value}`)
        .join(';');

      console.log('🔍 构建Cookie字符串完成，长度:', cookieString.length);

      // 保存到数据库的配置项
      const configs = [
        { name: 'wechat_article_data_ticket', value: cookieObject.data_ticket || '' },
        { name: 'wechat_article_rand_info', value: cookieObject.rand_info || '' },
        { name: 'wechat_article_bizuin', value: cookieObject.bizuin || '' },
        { name: 'wechat_article_slave_sid', value: cookieObject.slave_sid || '' },
        { name: 'wechat_article_slave_user', value: cookieObject.slave_user || '' },
        { name: 'wechat_article_token', value: this.session!.extractedToken || 'direct_save_token' },
        { name: 'wechat_article_cookie_string', value: cookieString },
        { name: 'wechat_article_session_id', value: this.session!.id },
        { name: 'wechat_article_login_time', value: new Date().toISOString() }
      ];

      console.log('🔍 开始保存配置项，总数:', configs.length);

      // 使用upsert更新或创建配置
      for (let i = 0; i < configs.length; i++) {
        const config = configs[i];
        console.log(`🔍 保存配置 ${i + 1}/${configs.length}: ${config.name}`);

        try {
          await prisma.systemConfig.upsert({
            where: { name: config.name },
            update: {
              value: config.value,
              updatedAt: new Date()
            },
            create: {
              name: config.name,
              value: config.value
            }
          });
          console.log(`✅ 配置 ${config.name} 保存成功`);
        } catch (configError) {
          console.error(`❌ 配置 ${config.name} 保存失败:`, configError);
          throw configError;
        }
      }

      console.log('✅ 微信登录Cookie已保存到数据库');

      // 断开连接
      await prisma.$disconnect();
      console.log('🔌 数据库连接已断开（Cookie保存）');
    } catch (error) {
      console.error('❌ 直接保存Cookie失败:', error);
      throw error;
    }
  }
}

export { WechatCrawlerLogin, type LoginSession, type WechatLoginOptions };