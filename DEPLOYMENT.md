# FeedWe 部署指南

## 部署架构

FeedWe 采用分离式部署架构，将 Web 应用和定时任务分别部署：

```
┌─────────────────┐    ┌─────────────────┐
│   Vercel 部署   │    │  Docker 服务    │
│                 │    │                 │
│ • Web 界面      │    │ • 定时爬取任务  │
│ • API 服务      │    │ • Webhook 通知  │
│ • 用户管理      │    │ • 健康检查      │
│ • 手动爬取      │    │                 │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
                共享数据库
```

## 第一步：Vercel 部署（Web 应用）

### 1. 准备 Vercel 环境变量

在 Vercel Dashboard 中设置以下环境变量：

```env
# 数据库连接
DATABASE_URL="mysql://username:password@host:port/database_name"

# JWT 密钥
JWT_SECRET="your_jwt_secret_here"

# 禁用定时任务（必须设置为 true）
DISABLE_CRAWLER_SCHEDULER=true

# 微信公众号配置（用于手动爬取）
WECHAT_ARTICLE_COOKIE_STRING="your_wechat_cookie_string_here"
WECHAT_ARTICLE_TOKEN="your_wechat_token_here"

# 生产环境标识
NODE_ENV=production
```

### 2. 部署到 Vercel

```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

或者通过 GitHub 集成自动部署。

### 3. 验证 Vercel 部署

访问你的 Vercel 应用，确认：
- ✅ Web 界面正常访问
- ✅ 用户注册/登录功能正常
- ✅ 手动爬取功能正常
- ✅ 定时任务已被禁用（在日志中应该看到相关提示）

## 第二步：Docker 部署（定时任务服务）

### 1. 准备服务器环境

确保服务器已安装：
- Docker
- Docker Compose
- Git

### 2. 克隆代码

```bash
git clone <your-repository-url>
cd FeedWe
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.crawler.example .env.crawler

# 编辑环境变量
nano .env.crawler
```

配置内容：
```env
# 数据库连接（与 Vercel 使用相同数据库）
DATABASE_URL="mysql://username:password@host:port/database_name"

# 微信公众号爬虫配置
WECHAT_ARTICLE_COOKIE_STRING="your_wechat_cookie_string_here"
WECHAT_ARTICLE_TOKEN="your_wechat_token_here"

# 企业微信Webhook配置（可选）
WEWORK_WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_webhook_key"

# 定时任务配置
DISABLE_CRAWLER_SCHEDULER=false

# 时区配置
TZ=Asia/Shanghai
```

### 4. 部署定时任务服务

```bash
# 给脚本添加执行权限
chmod +x docker/deploy.sh
chmod +x docker/start.sh

# 一键部署
./docker/deploy.sh
```

### 5. 验证 Docker 部署

```bash
# 检查服务状态
curl http://localhost:3001/health

# 查看服务日志
docker-compose -f docker-compose.crawler.yml logs -f

# 检查定时任务状态
curl http://localhost:3001/status
```

## 配置说明

### 微信登录凭据获取

1. 打开微信公众平台后台
2. 登录后打开开发者工具（F12）
3. 在 Network 标签页中找到任意一个请求
4. 复制 Cookie 中的完整字符串作为 `WECHAT_ARTICLE_COOKIE_STRING`
5. 从请求参数中找到 `token` 值作为 `WECHAT_ARTICLE_TOKEN`

### Webhook 配置

#### 企业微信
1. 在企业微信群中添加机器人
2. 获取 Webhook URL
3. 设置 `WEWORK_WEBHOOK_URL` 环境变量

#### 钉钉和飞书
类似配置，在用户的 Webhook 设置中配置相应的 URL。

## 监控和维护

### 日常监控

```bash
# 检查 Vercel 应用状态
curl https://your-app.vercel.app/api/init

# 检查 Docker 服务状态
curl http://your-server:3001/health

# 查看定时任务日志
docker-compose -f docker-compose.crawler.yml logs --tail=100
```

### 故障排除

1. **Vercel 应用问题**
   - 检查环境变量配置
   - 查看 Vercel 部署日志
   - 确认数据库连接

2. **Docker 服务问题**
   - 检查容器状态：`docker ps`
   - 查看服务日志：`docker-compose -f docker-compose.crawler.yml logs`
   - 重启服务：`docker-compose -f docker-compose.crawler.yml restart`

3. **定时任务不工作**
   - 检查微信登录凭据是否有效
   - 确认 `DISABLE_CRAWLER_SCHEDULER=false`
   - 查看定时任务执行日志

### 更新部署

```bash
# 更新 Vercel 应用
git push origin main  # 如果配置了自动部署

# 更新 Docker 服务
git pull origin main
./docker/deploy.sh
```

## 安全建议

1. **环境变量安全**
   - 不要将 `.env.crawler` 文件提交到版本控制
   - 定期更换 JWT_SECRET
   - 保护微信登录凭据

2. **网络安全**
   - 使用防火墙限制 Docker 服务端口访问
   - 配置 HTTPS（推荐使用 Nginx 反向代理）
   - 定期更新系统和 Docker 镜像

3. **数据安全**
   - 定期备份数据库
   - 监控异常访问
   - 设置告警机制

## 成本优化

- **Vercel**: 免费版本足够支持中小规模使用
- **Docker 服务**: 可以使用低配置 VPS（1核1G内存即可）
- **数据库**: 可以使用 PlanetScale、Supabase 等免费数据库服务

## 技术支持

如果遇到部署问题，请：

1. 查看相关日志文件
2. 检查环境变量配置
3. 确认网络连接正常
4. 参考故障排除章节

详细的 Docker 部署说明请参考 [Docker 部署指南](docs/docker-deployment.md)。
